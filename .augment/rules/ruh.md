---
type: "manual"
---

ALWAYS use powershell syntax
Use poetry for python run commands
Always Follow Test Driven development
1. Write a test first, based on the feature or behavior. If  working on a pre-existing feature check for tests. If any. Re-Align them based on the required behaviour or feature. If None exist. create one.
2. Run the test, which will fail (because the feature doesn't exist yet).
3. Write the minimum code required to make the test pass.
4. Run all tests to ensure nothing breaks.
5. Refactor the code to improve it without changing behavior.
6. Make sure to follow the project's code and file structure. Point out any issues in the code and file structure before making direct changes to the same.
7. Make sure you refer to the task list created by the user
8. Tick off the tasks as you complete them. Also suggest the next task to be completed according to priority after you finish a task.