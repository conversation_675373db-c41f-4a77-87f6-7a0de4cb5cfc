2025-07-01 12:46:11 - Main - INFO - Starting Server
2025-07-01 12:46:11 - Main - INFO - Connection at: **************:9092
2025-07-01 12:46:11 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-01 12:46:11 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-07-01 12:46:11 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-01 12:46:11 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-07-01 12:46:11 - Red<PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 12:46:13 - Red<PERSON>Manager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 12:46:13 - Red<PERSON>Manager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 12:46:15 - <PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - Successfully connected to Redis on DB index: 6!
