2025-07-01 15:17:52 - Main - INFO - Starting Server
2025-07-01 15:17:52 - Main - INFO - Connection at: **************:9092
2025-07-01 15:17:52 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-01 15:17:52 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-07-01 15:17:52 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-01 15:17:52 - Ka<PERSON>kaWorkflowConsumer - INFO - Initializing database connections...
2025-07-01 15:17:52 - <PERSON><PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 15:17:54 - <PERSON><PERSON>Manager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 15:17:54 - Red<PERSON>Manager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
