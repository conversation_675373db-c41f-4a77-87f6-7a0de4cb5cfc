2025-07-01 15:57:50 - Main - INFO - Starting Server
2025-07-01 15:57:50 - Main - INFO - Connection at: **************:9092
2025-07-01 15:57:50 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-01 15:57:50 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-07-01 15:57:50 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-01 15:57:50 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-07-01 15:57:50 - <PERSON><PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 15:57:52 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 15:57:52 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
