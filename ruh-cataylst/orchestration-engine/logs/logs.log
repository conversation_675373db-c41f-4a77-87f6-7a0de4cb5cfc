2025-07-01 13:47:12 - Main - INFO - Starting Server
2025-07-01 13:47:12 - Main - INFO - Connection at: **************:9092
2025-07-01 13:47:12 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-01 13:47:12 - NodeExecutor - INFO - NodeExecutor initialized.
2025-07-01 13:47:12 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-01 13:47:12 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-07-01 13:47:12 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 13:47:13 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 13:47:13 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 13:47:15 - <PERSON><PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 13:47:17 - PostgresManager - INFO - PostgreSQL connection pool created
2025-07-01 13:47:17 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-07-01 13:47:19 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-07-01 13:47:20 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-07-01 13:47:20 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 13:47:21 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 13:47:21 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 13:47:23 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 13:47:23 - RedisEventListener - INFO - Starting Redis event listener thread
2025-07-01 13:47:23 - RedisEventListener - INFO - Redis event listener started
2025-07-01 13:47:23 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-07-01 13:47:23 - StateManager - DEBUG - Using provided database connections
2025-07-01 13:47:23 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 13:47:23 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 13:47:23 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 13:47:24 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-07-01 13:47:24 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 13:47:24 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 13:47:24 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-07-01 13:47:24 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-07-01 13:47:24 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-07-01 13:47:24 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-07-01 13:47:27 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-07-01 13:47:27 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-07-01 13:47:27 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-07-01 13:47:31 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-07-01 13:47:38 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-07-01 13:47:38 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-07-01 13:47:38 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-07-01 13:47:44 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-07-01 13:47:44 - NodeExecutor - INFO - Background result consumer loop started.
2025-07-01 13:47:44 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-07-01 13:47:45 - NodeExecutor - DEBUG - Result consumer received message: Offset=1003
2025-07-01 13:47:45 - NodeExecutor - WARNING - Received result for unknown or timed-out request_id: 2ff79a53-6b55-4eed-af2f-8376ed32ab44
2025-07-01 13:47:46 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-AgenticAI-*************', 'data': b'expired'}
2025-07-01 13:47:46 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-AgenticAI-*************'
2025-07-01 13:47:46 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-AgenticAI-*************
2025-07-01 13:47:46 - RedisEventListener - DEBUG - Extracted key: result:transition-AgenticAI-*************
2025-07-01 13:47:46 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 13:47:46 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 13:47:46 - RedisEventListener - INFO - Detected expired event for result of transition: transition-AgenticAI-*************
2025-07-01 13:47:46 - RedisEventListener - INFO - Archiving result for transition: transition-AgenticAI-*************
2025-07-01 13:47:46 - StateManager - DEBUG - Attempting to archive result for transition transition-AgenticAI-*************
2025-07-01 13:47:46 - StateManager - DEBUG - Provided result: False
2025-07-01 13:47:47 - StateManager - DEBUG - Trying to get result from Redis for transition transition-AgenticAI-*************
2025-07-01 13:47:47 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-*************
2025-07-01 13:47:47 - StateManager - DEBUG - Trying to get result from memory for transition transition-AgenticAI-*************
2025-07-01 13:47:47 - StateManager - DEBUG - No result found in memory for transition transition-AgenticAI-*************
2025-07-01 13:47:47 - StateManager - DEBUG - Available transition results in memory: []
2025-07-01 13:47:47 - StateManager - WARNING - No result found to archive for transition transition-AgenticAI-*************
2025-07-01 13:47:50 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-AgenticAI-*************', 'data': b'expired'}
2025-07-01 13:47:50 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-AgenticAI-*************'
2025-07-01 13:47:50 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-AgenticAI-*************
2025-07-01 13:47:50 - RedisEventListener - DEBUG - Extracted key: result:transition-AgenticAI-*************
2025-07-01 13:47:50 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 13:47:50 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 13:47:50 - RedisEventListener - INFO - Detected expired event for result of transition: transition-AgenticAI-*************
2025-07-01 13:47:50 - RedisEventListener - INFO - Archiving result for transition: transition-AgenticAI-*************
2025-07-01 13:47:50 - StateManager - DEBUG - Attempting to archive result for transition transition-AgenticAI-*************
2025-07-01 13:47:51 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-07-01 13:47:51 - AgentExecutor - INFO - Background result consumer loop started.
2025-07-01 13:47:51 - StateManager - DEBUG - Provided result: False
2025-07-01 13:47:51 - StateManager - DEBUG - Trying to get result from Redis for transition transition-AgenticAI-*************
2025-07-01 13:47:51 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-*************
2025-07-01 13:47:51 - StateManager - DEBUG - Trying to get result from memory for transition transition-AgenticAI-*************
2025-07-01 13:47:51 - StateManager - DEBUG - No result found in memory for transition transition-AgenticAI-*************
2025-07-01 13:47:51 - StateManager - DEBUG - Available transition results in memory: []
2025-07-01 13:47:51 - StateManager - WARNING - No result found to archive for transition transition-AgenticAI-*************
2025-07-01 13:48:19 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1139
2025-07-01 13:48:19 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751357899, 'task_type': 'workflow', 'data': {'workflow_id': '0c19c070-905e-46ef-9f57-62eb427bf396', 'payload': {'user_dependent_fields': ['query'], 'user_payload_template': {'query': {'value': 'nature 101', 'transition_id': 'AgenticAI-*************'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-07-01 13:48:19 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: 0c19c070-905e-46ef-9f57-62eb427bf396
2025-07-01 13:48:19 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/0c19c070-905e-46ef-9f57-62eb427bf396
2025-07-01 13:48:20 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 13:48:20 - WorkflowService - DEBUG - Received response with status code: 200
2025-07-01 13:48:20 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Ruh_Video_Generation retrieved successfully",
  "workflow": {
    "id": "0c19c070-905e-46ef-9f57-62eb427bf396",
    "name": "Ruh_Video_Generation",
    "description": "Ruh_Video_Generation",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/53d754cc-3d77-459b-86d5-122f093848ed.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/3af6a43b-ebe5-41a9-881b-ed58d58d30d1.json",
    "start_nodes": [
      {
        "field": "query",
        "type": "string",
        "transition_id": "transition-AgenticAI-*************"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": "8d4b3f88-71ea-48dd-b8a2-dc101d22bf8a",
    "template_owner_id": "180c7469-1db0-4707-bc17-eeaa5e7ff64d",
    "is_imported": true,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-29T08:29:44.634200",
    "updated_at": "2025-07-01T08:15:18.296948",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "AgenticAI",
        "display_name": "AI Agent Executor",
        "type": "agent",
        "transition_id": "transition-AgenticAI-*************"
      },
      {
        "name": "AgenticAI",
        "display_name": "AI Agent Executor",
        "type": "agent",
        "transition_id": "transition-AgenticAI-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-1751005477464"
      },
      {
        "name": "AgenticAI",
        "display_name": "AI Agent Executor",
        "type": "agent",
        "transition_id": "transition-AgenticAI-1750505490787"
      },
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-1750920624318"
      },
      {
        "name": "AgenticAI",
        "display_name": "AI Agent Executor",
        "type": "agent",
        "transition_id": "transition-AgenticAI-1751001471087"
      },
      {
        "name": "AgenticAI",
        "display_name": "AI Agent Executor",
        "type": "agent",
        "transition_id": "transition-AgenticAI-1751001474606"
      }
    ],
    "is_updated": true
  }
}
2025-07-01 13:48:20 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 13:48:20 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 13:48:20 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 13:48:24 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 0c19c070-905e-46ef-9f57-62eb427bf396 - server_script_path is optional
2025-07-01 13:48:24 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-07-01 13:48:24 - StateManager - DEBUG - Using global database connections from initializer
2025-07-01 13:48:24 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 13:48:24 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 13:48:24 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 13:48:25 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 13:48:25 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 13:48:25 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-07-01 13:48:25 - StateManager - DEBUG - Using provided database connections
2025-07-01 13:48:25 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 13:48:25 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 13:48:25 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 13:48:25 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 13:48:25 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 13:48:25 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-*************: ['transition-AgenticAI-*************']
2025-07-01 13:48:25 - StateManager - DEBUG - Extracted dependencies for transition transition-LoopNode-*************: ['transition-AgenticAI-*************']
2025-07-01 13:48:25 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 13:48:25 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-1751005477464: ['transition-LoopNode-*************']
2025-07-01 13:48:25 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-*************']
2025-07-01 13:48:25 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-07-01 13:48:25 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-07-01 13:48:25 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 13:48:25 - StateManager - INFO - Built dependency map for 9 transitions
2025-07-01 13:48:25 - StateManager - DEBUG - Transition transition-AgenticAI-************* depends on: ['transition-AgenticAI-*************']
2025-07-01 13:48:25 - StateManager - DEBUG - Transition transition-LoopNode-************* depends on: ['transition-AgenticAI-*************']
2025-07-01 13:48:25 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 13:48:25 - StateManager - DEBUG - Transition transition-MergeDataComponent-1751005477464 depends on: ['transition-LoopNode-*************']
2025-07-01 13:48:25 - StateManager - DEBUG - Transition transition-AgenticAI-1750505490787 depends on: ['transition-CombineTextComponent-*************']
2025-07-01 13:48:25 - StateManager - DEBUG - Transition transition-AgenticAI-1751001471087 depends on: ['transition-AgenticAI-1750505490787']
2025-07-01 13:48:25 - StateManager - DEBUG - Transition transition-AgenticAI-1751001474606 depends on: ['transition-AgenticAI-1750505490787']
2025-07-01 13:48:25 - StateManager - DEBUG - Transition transition-CombineTextComponent-1750920624318 depends on: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 13:48:25 - MCPToolExecutor - DEBUG - Set correlation ID to: f548dc77-366a-45c1-8398-e3aa2e2afa8d
2025-07-01 13:48:25 - EnhancedWorkflowEngine - DEBUG - Set correlation_id f548dc77-366a-45c1-8398-e3aa2e2afa8d in tool_executor
2025-07-01 13:48:25 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-07-01 13:48:25 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-07-01 13:48:25 - NodeExecutor - DEBUG - Set correlation ID to: f548dc77-366a-45c1-8398-e3aa2e2afa8d
2025-07-01 13:48:25 - EnhancedWorkflowEngine - DEBUG - Set correlation_id f548dc77-366a-45c1-8398-e3aa2e2afa8d in node_executor
2025-07-01 13:48:25 - AgentExecutor - DEBUG - Set correlation ID to: f548dc77-366a-45c1-8398-e3aa2e2afa8d
2025-07-01 13:48:25 - EnhancedWorkflowEngine - DEBUG - Set correlation_id f548dc77-366a-45c1-8398-e3aa2e2afa8d in agent_executor
2025-07-01 13:48:25 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-07-01 13:48:25 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-07-01 13:48:25 - TransitionHandler - INFO - TransitionHandler initialized
2025-07-01 13:48:25 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d
2025-07-01 13:48:25 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: f548dc77-366a-45c1-8398-e3aa2e2afa8d
2025-07-01 13:48:25 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-07-01 13:48:25 - StateManager - INFO - Workflow initialized with initial transition: transition-AgenticAI-*************
2025-07-01 13:48:25 - StateManager - DEBUG - State: pending={'transition-AgenticAI-*************'}, waiting=set(), completed=set()
2025-07-01 13:48:25 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-AgenticAI-*************
2025-07-01 13:48:25 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-07-01 13:48:26 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:f548dc77-366a-45c1-8398-e3aa2e2afa8d'
2025-07-01 13:48:27 - RedisManager - DEBUG - Set key 'workflow_state:f548dc77-366a-45c1-8398-e3aa2e2afa8d' with TTL of 600 seconds
2025-07-01 13:48:27 - StateManager - INFO - Workflow state saved to Redis for workflow ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 13:48:27 - StateManager - DEBUG - Checking waiting transitions: set()
2025-07-01 13:48:27 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-01 13:48:27 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-07-01 13:48:27 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 13:48:27 - StateManager - INFO - Terminated: False
2025-07-01 13:48:27 - StateManager - INFO - Pending transitions (0): []
2025-07-01 13:48:27 - StateManager - INFO - Waiting transitions (0): []
2025-07-01 13:48:27 - StateManager - INFO - Completed transitions (0): []
2025-07-01 13:48:27 - StateManager - INFO - Results stored for 0 transitions
2025-07-01 13:48:27 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-01 13:48:27 - StateManager - INFO - Workflow status: inactive
2025-07-01 13:48:27 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-01 13:48:27 - StateManager - INFO - Workflow status: inactive
2025-07-01 13:48:27 - StateManager - INFO - Workflow paused: False
2025-07-01 13:48:27 - StateManager - INFO - ==============================
2025-07-01 13:48:27 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-07-01 13:48:27 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id f548dc77-366a-45c1-8398-e3aa2e2afa8d):
2025-07-01 13:48:27 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-07-01 13:48:27 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=initial, execution_type=agent)
2025-07-01 13:48:27 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-07-01 13:48:27 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-07-01 13:48:27 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-07-01 13:48:27 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-07-01 13:48:27 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-07-01 13:48:27 - WorkflowUtils - DEBUG - Filtering out field 'description' with null/empty value: 
2025-07-01 13:48:27 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-07-01 13:48:27 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with empty collection: {}
2025-07-01 13:48:27 - TransitionHandler - DEBUG - tool Parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 13:48:27 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 13:48:27 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id f548dc77-366a-45c1-8398-e3aa2e2afa8d):
2025-07-01 13:48:27 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-07-01 13:48:27 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: ac53964a-6594-449e-8e7f-c52384e7cb7f) with correlation_id: f548dc77-366a-45c1-8398-e3aa2e2afa8d, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-07-01 13:48:27 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-07-01 13:48:27 - AgentExecutor - DEBUG - Added correlation_id f548dc77-366a-45c1-8398-e3aa2e2afa8d to payload
2025-07-01 13:48:27 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': 'ac53964a-6594-449e-8e7f-c52384e7cb7f', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': 'f548dc77-366a-45c1-8398-e3aa2e2afa8d', 'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'variables': {}, 'agent_config': {'id': 'ae284af4-6a25-4ac0-9dbd-43dac10c841b', 'name': 'AI Agent', 'description': 'Basic AI Agent', 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-07-01 13:48:27 - AgentExecutor - DEBUG - Request ac53964a-6594-449e-8e7f-c52384e7cb7f sent successfully using provided producer.
2025-07-01 13:48:27 - AgentExecutor - DEBUG - Waiting for single response result for request ac53964a-6594-449e-8e7f-c52384e7cb7f...
2025-07-01 13:48:27 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1139, corr_id: f548dc77-366a-45c1-8398-e3aa2e2afa8d
2025-07-01 13:48:31 - AgentExecutor - DEBUG - Result consumer received message: Offset=24529
2025-07-01 13:48:31 - AgentExecutor - DEBUG - Processing result payload: {'run_id': 'ac53964a-6594-449e-8e7f-c52384e7cb7f', 'session_id': 'ac53964a-6594-449e-8e7f-c52384e7cb7f', 'event_type': None, 'agent_response': {'content': 'Error: Connection error.'}, 'success': False, 'message': 'Message processed successfully', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'component', 'request_id': 'ac53964a-6594-449e-8e7f-c52384e7cb7f'}
2025-07-01 13:48:31 - AgentExecutor - WARNING - Received connection error for request ac53964a-6594-449e-8e7f-c52384e7cb7f, but continuing to wait for potential success response
2025-07-01 13:48:34 - AgentExecutor - DEBUG - Result consumer received message: Offset=24530
2025-07-01 13:48:34 - AgentExecutor - DEBUG - Processing result payload: {'run_id': 'ac53964a-6594-449e-8e7f-c52384e7cb7f', 'session_id': 'ac53964a-6594-449e-8e7f-c52384e7cb7f', 'event_type': None, 'agent_response': {'content': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'source': 'ai_agent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'TextMessage', 'metadata': {}}, 'success': True, 'message': 'Message processed successfully', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'component', 'request_id': 'ac53964a-6594-449e-8e7f-c52384e7cb7f', 'error_code': None, 'details': None}
2025-07-01 13:48:34 - AgentExecutor - DEBUG - Agent response extracted: {'content': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'source': 'ai_agent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'TextMessage', 'metadata': {}}
2025-07-01 13:48:34 - AgentExecutor - DEBUG - Content extracted: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "pa...
2025-07-01 13:48:34 - AgentExecutor - DEBUG - Received valid result for request_id ac53964a-6594-449e-8e7f-c52384e7cb7f
2025-07-01 13:48:34 - AgentExecutor - INFO - Single response received for request ac53964a-6594-449e-8e7f-c52384e7cb7f.
2025-07-01 13:48:34 - TransitionHandler - INFO - Execution result from agent executor: "[ { \"part\": \"(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)\" }, { \"part\": \"Narrator: \"Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?\"\" }, { \"part\": \"(Close-ups of the iPhone\u2019s minimalist and advanced design.)\\n\\nNarrator: \"Check this out \u2013 the latest iPhone redefines what a smartphone can do! Isn't that wild?\"\" }, { \"part\": \"(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: \"Start your day... like, super smoothly. The new chip launches apps faster than ever\u2014whether you\u2019re keeping up with emails or sharing those killer moments!\"\" }, { \"part\": \"(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: \"Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow.\"\" }, { \"part\": \"(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: \"Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?\"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)\" } ]"
2025-07-01 13:48:34 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id f548dc77-366a-45c1-8398-e3aa2e2afa8d):
2025-07-01 13:48:34 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition Result received.', 'result': {'data': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'data_type': 'string', 'semantic_type': 'string', 'property_name': 'content'}, 'status': 'completed', 'sequence': 2, 'workflow_status': 'running', 'approval_required': False}
2025-07-01 13:48:34 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in memory: {'AgenticAI': {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}, 'status': 'completed', 'timestamp': 1751357914.3356118}}
2025-07-01 13:48:35 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-AgenticAI-*************'
2025-07-01 13:48:35 - RedisManager - DEBUG - Set key 'result:transition-AgenticAI-*************' with TTL of 300 seconds
2025-07-01 13:48:35 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 13:48:35 - StateManager - INFO - Marked transition transition-AgenticAI-************* as completed (was_pending=False, was_waiting=False)
2025-07-01 13:48:35 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-AgenticAI-*************'}
2025-07-01 13:48:35 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-AgenticAI-*************
2025-07-01 13:48:35 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-07-01 13:48:35 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-07-01 13:48:35 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-07-01 13:48:35 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-AgenticAI-*************:
2025-07-01 13:48:35 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-01 13:48:35 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-01 13:48:35 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-AgenticAI-*************, returning empty list
2025-07-01 13:48:35 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-AgenticAI-*************
2025-07-01 13:48:35 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 2
2025-07-01 13:48:35 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']
2025-07-01 13:48:35 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-01 13:48:35 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-01 13:48:35 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-AgenticAI-*************
2025-07-01 13:48:35 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-CombineTextComponent-*************
2025-07-01 13:48:35 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']
2025-07-01 13:48:35 - TransitionHandler - INFO - Completed transition transition-AgenticAI-************* in 8.25 seconds
2025-07-01 13:48:35 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id f548dc77-366a-45c1-8398-e3aa2e2afa8d):
2025-07-01 13:48:35 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d, response: {'result': 'Completed transition in 8.25 seconds', 'message': 'Transition completed in 8.25 seconds', 'transition_id': 'transition-AgenticAI-*************', 'status': 'time_logged', 'sequence': 3, 'workflow_status': 'running'}
2025-07-01 13:48:35 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-AgenticAI-*************: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']
2025-07-01 13:48:35 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 2
2025-07-01 13:48:35 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']]
2025-07-01 13:48:35 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-*************: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']
2025-07-01 13:48:35 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-01 13:48:35 - EnhancedWorkflowEngine - INFO - Transition transition-AgenticAI-************* completed successfully: 2 next transitions
2025-07-01 13:48:35 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']
2025-07-01 13:48:35 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']
2025-07-01 13:48:35 - EnhancedWorkflowEngine - INFO - Adding transition transition-AgenticAI-************* to pending (all dependencies met)
2025-07-01 13:48:35 - EnhancedWorkflowEngine - INFO - Adding transition transition-CombineTextComponent-************* to waiting (dependencies not yet met)
2025-07-01 13:48:35 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-07-01 13:48:35 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:f548dc77-366a-45c1-8398-e3aa2e2afa8d'
2025-07-01 13:48:36 - RedisManager - DEBUG - Set key 'workflow_state:f548dc77-366a-45c1-8398-e3aa2e2afa8d' with TTL of 600 seconds
2025-07-01 13:48:36 - StateManager - INFO - Workflow state saved to Redis for workflow ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 13:48:36 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-07-01 13:48:36 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-01 13:48:36 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-07-01 13:48:36 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 13:48:36 - StateManager - INFO - Terminated: False
2025-07-01 13:48:36 - StateManager - INFO - Pending transitions (0): []
2025-07-01 13:48:36 - StateManager - INFO - Waiting transitions (1): ['transition-CombineTextComponent-*************']
2025-07-01 13:48:36 - StateManager - INFO - Completed transitions (1): ['transition-AgenticAI-*************']
2025-07-01 13:48:36 - StateManager - INFO - Results stored for 1 transitions
2025-07-01 13:48:36 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 13:48:36 - StateManager - INFO - Workflow status: active
2025-07-01 13:48:36 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 13:48:36 - StateManager - INFO - Workflow status: active
2025-07-01 13:48:36 - StateManager - INFO - Workflow paused: False
2025-07-01 13:48:36 - StateManager - INFO - ==============================
2025-07-01 13:48:36 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-07-01 13:48:36 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id f548dc77-366a-45c1-8398-e3aa2e2afa8d):
2025-07-01 13:48:36 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 4, 'workflow_status': 'running'}
2025-07-01 13:48:36 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=standard, execution_type=agent)
2025-07-01 13:48:36 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-07-01 13:48:36 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-07-01 13:48:36 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-07-01 13:48:36 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-07-01 13:48:37 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 13:48:37 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 13:48:37 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 13:48:37 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 13:48:37 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 13:48:37 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:48:37 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:48:37 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:48:37 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:48:37 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:48:37 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:48:37 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-07-01 13:48:37 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-07-01 13:48:37 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:48:37 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:48:37 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:48:37 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:48:37 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:48:37 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:48:37 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → query via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:48:37 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-07-01 13:48:37 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-07-01 13:48:37 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-07-01 13:48:37 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with empty collection: {}
2025-07-01 13:48:37 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_type = component
2025-07-01 13:48:37 - TransitionHandler - DEBUG - 📌 Added static parameter: execution_type = response
2025-07-01 13:48:37 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_config = {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}
2025-07-01 13:48:37 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 13:48:37 - TransitionHandler - DEBUG - tool Parameters: {'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 13:48:37 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 13:48:37 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id f548dc77-366a-45c1-8398-e3aa2e2afa8d):
2025-07-01 13:48:37 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 5, 'workflow_status': 'running'}
2025-07-01 13:48:37 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 1e2ed831-18ed-4c2c-8755-cce06d42d31d) with correlation_id: f548dc77-366a-45c1-8398-e3aa2e2afa8d, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-07-01 13:48:37 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-07-01 13:48:37 - AgentExecutor - DEBUG - Added correlation_id f548dc77-366a-45c1-8398-e3aa2e2afa8d to payload
2025-07-01 13:48:37 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '1e2ed831-18ed-4c2c-8755-cce06d42d31d', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': 'f548dc77-366a-45c1-8398-e3aa2e2afa8d', 'agent_type': 'component', 'execution_type': 'response', 'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'variables': {}, 'agent_config': {'id': '1c1a2440-7308-4707-a89c-2b301d62b99a', 'name': 'AI Agent', 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-07-01 13:48:37 - AgentExecutor - DEBUG - Request 1e2ed831-18ed-4c2c-8755-cce06d42d31d sent successfully using provided producer.
2025-07-01 13:48:37 - AgentExecutor - DEBUG - Waiting for single response result for request 1e2ed831-18ed-4c2c-8755-cce06d42d31d...
2025-07-01 13:48:38 - AgentExecutor - DEBUG - Result consumer received message: Offset=24531
2025-07-01 13:48:38 - AgentExecutor - DEBUG - Processing result payload: {'run_id': '1e2ed831-18ed-4c2c-8755-cce06d42d31d', 'session_id': '1e2ed831-18ed-4c2c-8755-cce06d42d31d', 'event_type': None, 'agent_response': {'content': '0', 'source': 'ai_agent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'TextMessage', 'metadata': {}}, 'success': True, 'message': 'Message processed successfully', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'component', 'request_id': '1e2ed831-18ed-4c2c-8755-cce06d42d31d', 'error_code': None, 'details': None}
2025-07-01 13:48:38 - AgentExecutor - DEBUG - Agent response extracted: {'content': '0', 'source': 'ai_agent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'TextMessage', 'metadata': {}}
2025-07-01 13:48:38 - AgentExecutor - DEBUG - Content extracted: 0...
2025-07-01 13:48:38 - AgentExecutor - DEBUG - Received valid result for request_id 1e2ed831-18ed-4c2c-8755-cce06d42d31d
2025-07-01 13:48:38 - AgentExecutor - INFO - Single response received for request 1e2ed831-18ed-4c2c-8755-cce06d42d31d.
2025-07-01 13:48:38 - TransitionHandler - INFO - Execution result from agent executor: "0"
2025-07-01 13:48:38 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id f548dc77-366a-45c1-8398-e3aa2e2afa8d):
2025-07-01 13:48:38 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition Result received.', 'result': {'data': '0', 'data_type': 'string', 'semantic_type': 'string', 'property_name': 'content'}, 'status': 'completed', 'sequence': 6, 'workflow_status': 'running', 'approval_required': False}
2025-07-01 13:48:38 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in memory: {'AgenticAI': {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': '0'}, 'status': 'completed', 'timestamp': 1751357918.6588101}}
2025-07-01 13:48:39 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-AgenticAI-*************'
2025-07-01 13:48:39 - RedisManager - DEBUG - Set key 'result:transition-AgenticAI-*************' with TTL of 300 seconds
2025-07-01 13:48:39 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 13:48:39 - StateManager - INFO - Marked transition transition-AgenticAI-************* as completed (was_pending=False, was_waiting=False)
2025-07-01 13:48:39 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'transition-AgenticAI-*************', 'transition-AgenticAI-*************'}
2025-07-01 13:48:39 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-AgenticAI-*************
2025-07-01 13:48:39 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-07-01 13:48:39 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-07-01 13:48:39 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-07-01 13:48:39 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-AgenticAI-*************:
2025-07-01 13:48:39 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-01 13:48:39 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-01 13:48:39 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-AgenticAI-*************, returning empty list
2025-07-01 13:48:39 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-AgenticAI-*************
2025-07-01 13:48:39 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-07-01 13:48:39 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-LoopNode-*************']
2025-07-01 13:48:39 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-01 13:48:39 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-01 13:48:39 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-LoopNode-*************
2025-07-01 13:48:39 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-LoopNode-*************']
2025-07-01 13:48:39 - TransitionHandler - INFO - Completed transition transition-AgenticAI-************* in 3.00 seconds
2025-07-01 13:48:39 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id f548dc77-366a-45c1-8398-e3aa2e2afa8d):
2025-07-01 13:48:39 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d, response: {'result': 'Completed transition in 3.00 seconds', 'message': 'Transition completed in 3.00 seconds', 'transition_id': 'transition-AgenticAI-*************', 'status': 'time_logged', 'sequence': 7, 'workflow_status': 'running'}
2025-07-01 13:48:39 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-AgenticAI-*************: ['transition-LoopNode-*************']
2025-07-01 13:48:39 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-07-01 13:48:39 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-LoopNode-*************']]
2025-07-01 13:48:39 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-*************: ['transition-LoopNode-*************']
2025-07-01 13:48:39 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-01 13:48:39 - EnhancedWorkflowEngine - INFO - Transition transition-AgenticAI-************* completed successfully: 1 next transitions
2025-07-01 13:48:39 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-LoopNode-*************']
2025-07-01 13:48:39 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-LoopNode-*************']
2025-07-01 13:48:39 - EnhancedWorkflowEngine - INFO - Adding transition transition-LoopNode-************* to pending (all dependencies met)
2025-07-01 13:48:39 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-07-01 13:48:39 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:f548dc77-366a-45c1-8398-e3aa2e2afa8d'
2025-07-01 13:48:40 - RedisManager - DEBUG - Set key 'workflow_state:f548dc77-366a-45c1-8398-e3aa2e2afa8d' with TTL of 600 seconds
2025-07-01 13:48:40 - StateManager - INFO - Workflow state saved to Redis for workflow ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 13:48:40 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-07-01 13:48:40 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-01 13:48:40 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-07-01 13:48:40 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 13:48:40 - StateManager - INFO - Terminated: False
2025-07-01 13:48:40 - StateManager - INFO - Pending transitions (0): []
2025-07-01 13:48:40 - StateManager - INFO - Waiting transitions (1): ['transition-CombineTextComponent-*************']
2025-07-01 13:48:40 - StateManager - INFO - Completed transitions (2): ['transition-AgenticAI-*************', 'transition-AgenticAI-*************']
2025-07-01 13:48:40 - StateManager - INFO - Results stored for 2 transitions
2025-07-01 13:48:40 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 13:48:40 - StateManager - INFO - Workflow status: active
2025-07-01 13:48:40 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 13:48:40 - StateManager - INFO - Workflow status: active
2025-07-01 13:48:40 - StateManager - INFO - Workflow paused: False
2025-07-01 13:48:40 - StateManager - INFO - ==============================
2025-07-01 13:48:40 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-07-01 13:48:40 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id f548dc77-366a-45c1-8398-e3aa2e2afa8d):
2025-07-01 13:48:40 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 8, 'workflow_status': 'running'}
2025-07-01 13:48:40 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=standard, execution_type=loop)
2025-07-01 13:48:40 - LoopExecutor - DEBUG - 🔗 Orchestration engine reference set in LoopExecutor
2025-07-01 13:48:40 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-07-01 13:48:40 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-07-01 13:48:40 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-07-01 13:48:40 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-07-01 13:48:40 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-07-01 13:48:41 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 13:48:41 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 13:48:41 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 13:48:41 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 13:48:41 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 13:48:41 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '0'}
2025-07-01 13:48:41 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:48:41 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: 0
2025-07-01 13:48:41 - WorkflowUtils - DEBUG - Found result.result: 0 (type: <class 'str'>)
2025-07-01 13:48:41 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:48:41 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:48:41 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-07-01 13:48:41 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-07-01 13:48:41 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '0'}
2025-07-01 13:48:41 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:48:41 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: 0
2025-07-01 13:48:41 - WorkflowUtils - DEBUG - Found result.result: 0 (type: <class 'str'>)
2025-07-01 13:48:41 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:48:41 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:48:41 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → start via path 'result': 0
2025-07-01 13:48:41 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-07-01 13:48:41 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-07-01 13:48:41 - WorkflowUtils - DEBUG - Filtering out field 'iteration_list' with empty collection: []
2025-07-01 13:48:41 - WorkflowUtils - DEBUG - Filtering out field 'start' with null/empty value: None
2025-07-01 13:48:41 - WorkflowUtils - INFO - 🧹 Parameter filtering: 14 → 12 fields (2 null/empty fields removed)
2025-07-01 13:48:41 - TransitionHandler - DEBUG - 📌 Added static parameter: source_type = number_range
2025-07-01 13:48:41 - TransitionHandler - DEBUG - 📌 Added static parameter: batch_size = 1
2025-07-01 13:48:41 - TransitionHandler - DEBUG - 📌 Added static parameter: end = 6
2025-07-01 13:48:41 - TransitionHandler - DEBUG - 📌 Added static parameter: step = 1
2025-07-01 13:48:41 - TransitionHandler - DEBUG - 📌 Added static parameter: parallel_execution = False
2025-07-01 13:48:41 - TransitionHandler - DEBUG - 📌 Added static parameter: max_concurrent = 3
2025-07-01 13:48:41 - TransitionHandler - DEBUG - 📌 Added static parameter: preserve_order = True
2025-07-01 13:48:41 - TransitionHandler - DEBUG - 📌 Added static parameter: iteration_timeout = 60
2025-07-01 13:48:41 - TransitionHandler - DEBUG - 📌 Added static parameter: aggregation_type = collect_all
2025-07-01 13:48:41 - TransitionHandler - DEBUG - 📌 Added static parameter: include_metadata = False
2025-07-01 13:48:41 - TransitionHandler - DEBUG - 📌 Added static parameter: on_iteration_error = retry_once
2025-07-01 13:48:41 - TransitionHandler - DEBUG - 📌 Added static parameter: include_errors = True
2025-07-01 13:48:41 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-07-01 13:48:41 - TransitionHandler - DEBUG - tool Parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-07-01 13:48:41 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-07-01 13:48:41 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id f548dc77-366a-45c1-8398-e3aa2e2afa8d):
2025-07-01 13:48:41 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 9, 'workflow_status': 'running'}
2025-07-01 13:48:41 - LoopExecutor - INFO - 🔄 Starting loop execution for transition transition-LoopNode-*************
2025-07-01 13:48:41 - LoopExecutor - INFO - 🔧 Resolving loop configuration for transition transition-LoopNode-*************
2025-07-01 13:48:41 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 13:48:41 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 13:48:41 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 13:48:41 - LoopExecutor - DEBUG - 📋 Resolved loop configuration: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 1, 'end': 6}, 'step': 1, 'source_type': 'number_range'}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}}
2025-07-01 13:48:41 - LoopStateManager - INFO - 🔧 Initializing loop state for loop loop_transition-LoopNode-*************_6e5fc279
2025-07-01 13:48:41 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_6e5fc279, transition_id: transition-LoopNode-*************
2025-07-01 13:48:41 - LoopStateManager - INFO - ✅ Loop state initialized: 6 iterations planned
2025-07-01 13:48:41 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-*************: ['transition-AgenticAI-*************']
2025-07-01 13:48:41 - StateManager - DEBUG - Extracted dependencies for transition transition-LoopNode-*************: ['transition-AgenticAI-*************']
2025-07-01 13:48:41 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 13:48:41 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-1751005477464: ['transition-LoopNode-*************']
2025-07-01 13:48:41 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-*************']
2025-07-01 13:48:41 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-07-01 13:48:41 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-07-01 13:48:41 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 13:48:41 - StateManager - INFO - Built dependency map for 9 transitions
2025-07-01 13:48:41 - StateManager - DEBUG - Transition transition-AgenticAI-************* depends on: ['transition-AgenticAI-*************']
2025-07-01 13:48:41 - StateManager - DEBUG - Transition transition-LoopNode-************* depends on: ['transition-AgenticAI-*************']
2025-07-01 13:48:41 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 13:48:41 - StateManager - DEBUG - Transition transition-MergeDataComponent-1751005477464 depends on: ['transition-LoopNode-*************']
2025-07-01 13:48:41 - StateManager - DEBUG - Transition transition-AgenticAI-1750505490787 depends on: ['transition-CombineTextComponent-*************']
2025-07-01 13:48:41 - StateManager - DEBUG - Transition transition-AgenticAI-1751001471087 depends on: ['transition-AgenticAI-1750505490787']
2025-07-01 13:48:41 - StateManager - DEBUG - Transition transition-AgenticAI-1751001474606 depends on: ['transition-AgenticAI-1750505490787']
2025-07-01 13:48:41 - StateManager - DEBUG - Transition transition-CombineTextComponent-1750920624318 depends on: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 13:48:41 - LoopStateManager - INFO - 🔧 Loop body configured - Entry: ['transition-CombineTextComponent-*************'], Exit: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:48:41 - LoopStateManager - DEBUG - 🔗 Loop body dependencies: {'transition-CombineTextComponent-1750920624318': [], 'transition-CombineTextComponent-*************': []}
2025-07-01 13:48:41 - LoopExecutor - INFO - 🔄 Executing iteration 1/6
2025-07-01 13:48:41 - LoopExecutor - INFO - 🔄 Executing iteration 1 with item: 1
2025-07-01 13:48:41 - LoopStateManager - INFO - 🚀 Starting iteration 1/6
2025-07-01 13:48:41 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_6e5fc279, transition_id: transition-LoopNode-*************
2025-07-01 13:48:41 - LoopStateManager - DEBUG - 📋 Initialized iteration state - Pending: ['transition-CombineTextComponent-*************'], Waiting: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:48:41 - LoopStateManager - DEBUG - 🔄 Cleared iteration state - Ready for new iteration
2025-07-01 13:48:41 - LoopExecutor - DEBUG - 🔄 Executing pending transitions: ['transition-CombineTextComponent-*************']
2025-07-01 13:48:41 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-07-01 13:48:41 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id f548dc77-366a-45c1-8398-e3aa2e2afa8d):
2025-07-01 13:48:41 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 10, 'workflow_status': 'running'}
2025-07-01 13:48:41 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-07-01 13:48:41 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 13:48:41 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-07-01 13:48:41 - TransitionHandler - DEBUG - 🔄 Found iteration context for loop execution: {'iteration_index': 0, 'iteration_item': 1, 'loop_id': 'loop_transition-LoopNode-*************_6e5fc279', 'transition_id': 'transition-LoopNode-*************'}
2025-07-01 13:48:41 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 13:48:41 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 13:48:42 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 13:48:42 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 13:48:42 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 13:48:42 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 13:48:43 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 13:48:45 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation f548dc77-366a-45c1-8398-e3aa2e2afa8d
2025-07-01 13:48:45 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-07-01 13:48:45 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-LoopNode-*************
2025-07-01 13:48:45 - TransitionHandler - DEBUG - 🔄 Adding iteration context for loop execution: {'iteration_index': 0, 'iteration_item': 1, 'loop_id': 'loop_transition-LoopNode-*************_6e5fc279', 'transition_id': 'transition-LoopNode-*************'}
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:48:45 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (1/2 compatible)
2025-07-01 13:48:45 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (1/2 compatible)
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {}
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 13:48:45 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-07-01 13:48:45 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/2 successful
2025-07-01 13:48:45 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-07-01 13:48:45 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/2 successful
2025-07-01 13:48:45 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: 
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 13:48:45 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 13:48:45 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 13:48:45 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-07-01 13:48:45 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 13:48:45 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 13:48:45 - TransitionHandler - DEBUG - tool Parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 13:48:45 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 13:48:45 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 11, corr_id f548dc77-366a-45c1-8398-e3aa2e2afa8d):
2025-07-01 13:48:45 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 11, 'workflow_status': 'running'}
2025-07-01 13:48:45 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 6d0df2cf-6978-4951-9ecb-e620fbaa4933) using provided producer.
2025-07-01 13:48:45 - NodeExecutor - DEBUG - Added correlation_id f548dc77-366a-45c1-8398-e3aa2e2afa8d to payload
2025-07-01 13:48:45 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-07-01 13:48:45 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '6d0df2cf-6978-4951-9ecb-e620fbaa4933', 'correlation_id': 'f548dc77-366a-45c1-8398-e3aa2e2afa8d', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-07-01 13:48:45 - NodeExecutor - DEBUG - Request 6d0df2cf-6978-4951-9ecb-e620fbaa4933 sent successfully using provided producer.
2025-07-01 13:48:45 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 6d0df2cf-6978-4951-9ecb-e620fbaa4933...
2025-07-01 13:48:45 - AgentExecutor - DEBUG - Result consumer received message: Offset=24532
2025-07-01 13:48:45 - AgentExecutor - DEBUG - Processing result payload: {'run_id': '1e2ed831-18ed-4c2c-8755-cce06d42d31d', 'session_id': '1e2ed831-18ed-4c2c-8755-cce06d42d31d', 'event_type': None, 'agent_response': {'content': 'Error: Connection error.'}, 'success': False, 'message': 'Message processed successfully', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'component', 'request_id': '1e2ed831-18ed-4c2c-8755-cce06d42d31d'}
2025-07-01 13:48:45 - AgentExecutor - WARNING - Received connection error for request 1e2ed831-18ed-4c2c-8755-cce06d42d31d, but continuing to wait for potential success response
2025-07-01 13:48:46 - NodeExecutor - DEBUG - Result consumer received message: Offset=1004
2025-07-01 13:48:46 - NodeExecutor - WARNING - Received error response for request_id 6d0df2cf-6978-4951-9ecb-e620fbaa4933: Error combining text for request_id 6d0df2cf-6978-4951-9ecb-e620fbaa4933: "Required field 'main_input' not found in parameters"
2025-07-01 13:48:46 - NodeExecutor - ERROR - Error during node execution 6d0df2cf-6978-4951-9ecb-e620fbaa4933: Node execution failed: Error combining text for request_id 6d0df2cf-6978-4951-9ecb-e620fbaa4933: "Required field 'main_input' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 6d0df2cf-6978-4951-9ecb-e620fbaa4933: "Required field 'main_input' not found in parameters"
2025-07-01 13:48:46 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-*************': Node execution failed: Error combining text for request_id 6d0df2cf-6978-4951-9ecb-e620fbaa4933: "Required field 'main_input' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 383, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 6d0df2cf-6978-4951-9ecb-e620fbaa4933: "Required field 'main_input' not found in parameters"

2025-07-01 13:48:46 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 12, corr_id f548dc77-366a-45c1-8398-e3aa2e2afa8d):
2025-07-01 13:48:46 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 6d0df2cf-6978-4951-9ecb-e620fbaa4933: "Required field \'main_input\' not found in parameters"', 'status': 'failed', 'sequence': 12, 'workflow_status': 'running'}
2025-07-01 13:48:46 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 6d0df2cf-6978-4951-9ecb-e620fbaa4933: "Required field 'main_input' not found in parameters"
2025-07-01 13:48:46 - LoopExecutor - ERROR - ❌ Failed to execute transition transition-CombineTextComponent-*************_iteration_0: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 6d0df2cf-6978-4951-9ecb-e620fbaa4933: "Required field 'main_input' not found in parameters"
2025-07-01 13:48:46 - LoopExecutor - DEBUG - ✅ Transition transition-CombineTextComponent-************* completed in iteration 0
2025-07-01 13:48:46 - LoopStateManager - DEBUG - ✅ Marked transition-CombineTextComponent-************* as completed
2025-07-01 13:48:46 - LoopStateManager - DEBUG - 🔓 Newly available transitions: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:48:46 - LoopExecutor - DEBUG - 🔄 Executing pending transitions: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:48:46 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-1750920624318
2025-07-01 13:48:46 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 13, corr_id f548dc77-366a-45c1-8398-e3aa2e2afa8d):
2025-07-01 13:48:46 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-1750920624318', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-1750920624318', 'status': 'started', 'sequence': 13, 'workflow_status': 'running'}
2025-07-01 13:48:46 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-1750920624318' (type=standard, execution_type=Components)
2025-07-01 13:48:46 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 13:48:46 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-1750920624318
2025-07-01 13:48:46 - TransitionHandler - DEBUG - 🔄 Found iteration context for loop execution: {'iteration_index': 0, 'iteration_item': 1, 'loop_id': 'loop_transition-LoopNode-*************_6e5fc279', 'transition_id': 'transition-LoopNode-*************'}
2025-07-01 13:48:46 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 13:48:46 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 13:48:47 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-1750505490787. Trying PostgreSQL next.
2025-07-01 13:48:49 - PostgresManager - DEBUG - No result found for transition transition-AgenticAI-1750505490787 in correlation f548dc77-366a-45c1-8398-e3aa2e2afa8d
2025-07-01 13:48:49 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-AgenticAI-1750505490787. Trying in-memory next.
2025-07-01 13:48:49 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-AgenticAI-1750505490787
2025-07-01 13:48:50 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-1751001471087. Trying PostgreSQL next.
2025-07-01 13:48:52 - PostgresManager - DEBUG - No result found for transition transition-AgenticAI-1751001471087 in correlation f548dc77-366a-45c1-8398-e3aa2e2afa8d
2025-07-01 13:48:52 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-07-01 13:48:52 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-07-01 13:48:52 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-07-01 13:48:52 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-07-01 13:48:52 - EnhancedWorkflowEngine - WARNING - Workflow f548dc77-366a-45c1-8398-e3aa2e2afa8d execution was cancelled!
2025-07-01 13:48:52 - StateManager - INFO - Workflow terminated flag set to: True
2025-07-01 13:48:52 - KafkaWorkflowConsumer - WARNING - Workflow execution for '0c19c070-905e-46ef-9f57-62eb427bf396' was cancelled
2025-07-01 13:48:52 - KafkaWorkflowConsumer - INFO - Workflow '0c19c070-905e-46ef-9f57-62eb427bf396' final status: cancelled, result: Workflow '0c19c070-905e-46ef-9f57-62eb427bf396' execution was cancelled
2025-07-01 13:48:52 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f548dc77-366a-45c1-8398-e3aa2e2afa8d, response: {'status': 'Workflow Cancelled', 'result': "Workflow '0c19c070-905e-46ef-9f57-62eb427bf396' execution was cancelled", 'workflow_status': 'cancelled'}
2025-07-01 13:48:52 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: f548dc77-366a-45c1-8398-e3aa2e2afa8d 
2025-07-01 13:48:52 - Main - ERROR - Shutting down due to keyboard interrupt...
