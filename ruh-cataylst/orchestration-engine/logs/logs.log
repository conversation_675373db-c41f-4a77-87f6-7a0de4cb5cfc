2025-07-01 13:55:30 - Main - INFO - Starting Server
2025-07-01 13:55:30 - Main - INFO - Connection at: **************:9092
2025-07-01 13:55:30 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-01 13:55:30 - NodeExecutor - INFO - NodeExecutor initialized.
2025-07-01 13:55:30 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-01 13:55:30 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-07-01 13:55:30 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 13:55:32 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 13:55:32 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 13:55:33 - <PERSON><PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 13:55:35 - PostgresManager - INFO - PostgreSQL connection pool created
2025-07-01 13:55:35 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-07-01 13:55:38 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-07-01 13:55:39 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-07-01 13:55:39 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 13:55:41 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 13:55:41 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 13:55:42 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 13:55:42 - RedisEventListener - INFO - Starting Redis event listener thread
2025-07-01 13:55:42 - RedisEventListener - INFO - Redis event listener started
2025-07-01 13:55:42 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-07-01 13:55:42 - StateManager - DEBUG - Using provided database connections
2025-07-01 13:55:42 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 13:55:42 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 13:55:42 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 13:55:43 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-07-01 13:55:43 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 13:55:43 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 13:55:43 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-07-01 13:55:43 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-07-01 13:55:43 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-07-01 13:55:43 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-07-01 13:55:46 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-07-01 13:55:46 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-07-01 13:55:46 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-07-01 13:55:52 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-07-01 13:55:58 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-07-01 13:55:58 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-07-01 13:55:58 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-07-01 13:56:04 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-07-01 13:56:04 - NodeExecutor - INFO - Background result consumer loop started.
2025-07-01 13:56:04 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-07-01 13:56:06 - NodeExecutor - DEBUG - Result consumer received message: Offset=1004
2025-07-01 13:56:06 - NodeExecutor - WARNING - Received result for unknown or timed-out request_id: 6d0df2cf-6978-4951-9ecb-e620fbaa4933
2025-07-01 13:56:11 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-07-01 13:56:11 - AgentExecutor - INFO - Background result consumer loop started.
2025-07-01 13:56:11 - AgentExecutor - DEBUG - Result consumer received message: Offset=24532
2025-07-01 13:56:11 - AgentExecutor - DEBUG - Processing result payload: {'run_id': '1e2ed831-18ed-4c2c-8755-cce06d42d31d', 'session_id': '1e2ed831-18ed-4c2c-8755-cce06d42d31d', 'event_type': None, 'agent_response': {'content': 'Error: Connection error.'}, 'success': False, 'message': 'Message processed successfully', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'component', 'request_id': '1e2ed831-18ed-4c2c-8755-cce06d42d31d'}
2025-07-01 13:56:11 - AgentExecutor - WARNING - Received connection error for request 1e2ed831-18ed-4c2c-8755-cce06d42d31d, but continuing to wait for potential success response
2025-07-01 13:56:17 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1140
2025-07-01 13:56:17 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751358377, 'task_type': 'workflow', 'data': {'workflow_id': '0c19c070-905e-46ef-9f57-62eb427bf396', 'payload': {'user_dependent_fields': ['query'], 'user_payload_template': {'query': {'value': 'nature 101', 'transition_id': 'AgenticAI-*************'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-07-01 13:56:17 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: 0c19c070-905e-46ef-9f57-62eb427bf396
2025-07-01 13:56:17 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/0c19c070-905e-46ef-9f57-62eb427bf396
2025-07-01 13:56:18 - WorkflowService - DEBUG - Received response with status code: 200
2025-07-01 13:56:18 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Ruh_Video_Generation retrieved successfully",
  "workflow": {
    "id": "0c19c070-905e-46ef-9f57-62eb427bf396",
    "name": "Ruh_Video_Generation",
    "description": "Ruh_Video_Generation",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/53d754cc-3d77-459b-86d5-122f093848ed.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/3af6a43b-ebe5-41a9-881b-ed58d58d30d1.json",
    "start_nodes": [
      {
        "field": "query",
        "type": "string",
        "transition_id": "transition-AgenticAI-*************"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": "8d4b3f88-71ea-48dd-b8a2-dc101d22bf8a",
    "template_owner_id": "180c7469-1db0-4707-bc17-eeaa5e7ff64d",
    "is_imported": true,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-29T08:29:44.634200",
    "updated_at": "2025-07-01T08:15:18.296948",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "AgenticAI",
        "display_name": "AI Agent Executor",
        "type": "agent",
        "transition_id": "transition-AgenticAI-*************"
      },
      {
        "name": "AgenticAI",
        "display_name": "AI Agent Executor",
        "type": "agent",
        "transition_id": "transition-AgenticAI-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-1751005477464"
      },
      {
        "name": "AgenticAI",
        "display_name": "AI Agent Executor",
        "type": "agent",
        "transition_id": "transition-AgenticAI-*************"
      },
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-1750920624318"
      },
      {
        "name": "AgenticAI",
        "display_name": "AI Agent Executor",
        "type": "agent",
        "transition_id": "transition-AgenticAI-1751001471087"
      },
      {
        "name": "AgenticAI",
        "display_name": "AI Agent Executor",
        "type": "agent",
        "transition_id": "transition-AgenticAI-1751001474606"
      }
    ],
    "is_updated": true
  }
}
2025-07-01 13:56:18 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 0c19c070-905e-46ef-9f57-62eb427bf396 - server_script_path is optional
2025-07-01 13:56:18 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-07-01 13:56:18 - StateManager - DEBUG - Using global database connections from initializer
2025-07-01 13:56:18 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 13:56:18 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 13:56:18 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 13:56:19 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 13:56:19 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 13:56:19 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-07-01 13:56:19 - StateManager - DEBUG - Using provided database connections
2025-07-01 13:56:19 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 13:56:19 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 13:56:19 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 13:56:20 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 13:56:20 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 13:56:20 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-*************: ['transition-AgenticAI-*************']
2025-07-01 13:56:20 - StateManager - DEBUG - Extracted dependencies for transition transition-LoopNode-*************: ['transition-AgenticAI-*************']
2025-07-01 13:56:20 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 13:56:20 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-1751005477464: ['transition-LoopNode-*************']
2025-07-01 13:56:20 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-*************: ['transition-CombineTextComponent-*************']
2025-07-01 13:56:20 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-*************']
2025-07-01 13:56:20 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-*************']
2025-07-01 13:56:20 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-*************', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 13:56:20 - StateManager - INFO - Built dependency map for 9 transitions
2025-07-01 13:56:20 - StateManager - DEBUG - Transition transition-AgenticAI-************* depends on: ['transition-AgenticAI-*************']
2025-07-01 13:56:20 - StateManager - DEBUG - Transition transition-LoopNode-************* depends on: ['transition-AgenticAI-*************']
2025-07-01 13:56:20 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 13:56:20 - StateManager - DEBUG - Transition transition-MergeDataComponent-1751005477464 depends on: ['transition-LoopNode-*************']
2025-07-01 13:56:20 - StateManager - DEBUG - Transition transition-AgenticAI-************* depends on: ['transition-CombineTextComponent-*************']
2025-07-01 13:56:20 - StateManager - DEBUG - Transition transition-AgenticAI-1751001471087 depends on: ['transition-AgenticAI-*************']
2025-07-01 13:56:20 - StateManager - DEBUG - Transition transition-AgenticAI-1751001474606 depends on: ['transition-AgenticAI-*************']
2025-07-01 13:56:20 - StateManager - DEBUG - Transition transition-CombineTextComponent-1750920624318 depends on: ['transition-AgenticAI-*************', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 13:56:20 - MCPToolExecutor - DEBUG - Set correlation ID to: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:56:20 - EnhancedWorkflowEngine - DEBUG - Set correlation_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe in tool_executor
2025-07-01 13:56:20 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-07-01 13:56:20 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-07-01 13:56:20 - NodeExecutor - DEBUG - Set correlation ID to: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:56:20 - EnhancedWorkflowEngine - DEBUG - Set correlation_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe in node_executor
2025-07-01 13:56:20 - AgentExecutor - DEBUG - Set correlation ID to: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:56:20 - EnhancedWorkflowEngine - DEBUG - Set correlation_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe in agent_executor
2025-07-01 13:56:20 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-07-01 13:56:20 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-07-01 13:56:20 - TransitionHandler - INFO - TransitionHandler initialized
2025-07-01 13:56:20 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:56:20 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:56:20 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-07-01 13:56:20 - StateManager - INFO - Workflow initialized with initial transition: transition-AgenticAI-*************
2025-07-01 13:56:20 - StateManager - DEBUG - State: pending={'transition-AgenticAI-*************'}, waiting=set(), completed=set()
2025-07-01 13:56:20 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-AgenticAI-*************
2025-07-01 13:56:20 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-07-01 13:56:21 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe'
2025-07-01 13:56:21 - RedisManager - DEBUG - Set key 'workflow_state:f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe' with TTL of 600 seconds
2025-07-01 13:56:21 - StateManager - INFO - Workflow state saved to Redis for workflow ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 13:56:21 - StateManager - DEBUG - Checking waiting transitions: set()
2025-07-01 13:56:21 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-01 13:56:21 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-07-01 13:56:21 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 13:56:21 - StateManager - INFO - Terminated: False
2025-07-01 13:56:21 - StateManager - INFO - Pending transitions (0): []
2025-07-01 13:56:21 - StateManager - INFO - Waiting transitions (0): []
2025-07-01 13:56:21 - StateManager - INFO - Completed transitions (0): []
2025-07-01 13:56:21 - StateManager - INFO - Results stored for 0 transitions
2025-07-01 13:56:21 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-01 13:56:21 - StateManager - INFO - Workflow status: inactive
2025-07-01 13:56:21 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-01 13:56:21 - StateManager - INFO - Workflow status: inactive
2025-07-01 13:56:21 - StateManager - INFO - Workflow paused: False
2025-07-01 13:56:21 - StateManager - INFO - ==============================
2025-07-01 13:56:21 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-07-01 13:56:21 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:56:21 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-07-01 13:56:21 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=initial, execution_type=agent)
2025-07-01 13:56:21 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-07-01 13:56:21 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-07-01 13:56:21 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-07-01 13:56:21 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-07-01 13:56:21 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-07-01 13:56:21 - WorkflowUtils - DEBUG - Filtering out field 'description' with null/empty value: 
2025-07-01 13:56:21 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-07-01 13:56:21 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with empty collection: {}
2025-07-01 13:56:21 - TransitionHandler - DEBUG - tool Parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 13:56:21 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 13:56:21 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:56:21 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-07-01 13:56:21 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: fd738264-9a5f-48bb-b97f-9c5f9d32b01f) with correlation_id: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-07-01 13:56:21 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-07-01 13:56:21 - AgentExecutor - DEBUG - Added correlation_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe to payload
2025-07-01 13:56:21 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': 'fd738264-9a5f-48bb-b97f-9c5f9d32b01f', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': 'f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe', 'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'variables': {}, 'agent_config': {'id': '2d517222-c26f-43c8-8c09-0fb18245c9cb', 'name': 'AI Agent', 'description': 'Basic AI Agent', 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-07-01 13:56:21 - AgentExecutor - DEBUG - Request fd738264-9a5f-48bb-b97f-9c5f9d32b01f sent successfully using provided producer.
2025-07-01 13:56:21 - AgentExecutor - DEBUG - Waiting for single response result for request fd738264-9a5f-48bb-b97f-9c5f9d32b01f...
2025-07-01 13:56:22 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1140, corr_id: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:56:26 - AgentExecutor - DEBUG - Result consumer received message: Offset=24533
2025-07-01 13:56:26 - AgentExecutor - DEBUG - Processing result payload: {'run_id': 'fd738264-9a5f-48bb-b97f-9c5f9d32b01f', 'session_id': 'fd738264-9a5f-48bb-b97f-9c5f9d32b01f', 'event_type': None, 'agent_response': {'content': 'Error: Connection error.'}, 'success': False, 'message': 'Message processed successfully', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'component', 'request_id': 'fd738264-9a5f-48bb-b97f-9c5f9d32b01f'}
2025-07-01 13:56:26 - AgentExecutor - WARNING - Received connection error for request fd738264-9a5f-48bb-b97f-9c5f9d32b01f, but continuing to wait for potential success response
2025-07-01 13:56:37 - AgentExecutor - DEBUG - Result consumer received message: Offset=24534
2025-07-01 13:56:37 - AgentExecutor - DEBUG - Processing result payload: {'run_id': 'fd738264-9a5f-48bb-b97f-9c5f9d32b01f', 'session_id': 'fd738264-9a5f-48bb-b97f-9c5f9d32b01f', 'event_type': None, 'agent_response': {'content': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'source': 'ai_agent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'TextMessage', 'metadata': {}}, 'success': True, 'message': 'Message processed successfully', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'component', 'request_id': 'fd738264-9a5f-48bb-b97f-9c5f9d32b01f', 'error_code': None, 'details': None}
2025-07-01 13:56:37 - AgentExecutor - DEBUG - Agent response extracted: {'content': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'source': 'ai_agent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'TextMessage', 'metadata': {}}
2025-07-01 13:56:37 - AgentExecutor - DEBUG - Content extracted: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "pa...
2025-07-01 13:56:37 - AgentExecutor - DEBUG - Received valid result for request_id fd738264-9a5f-48bb-b97f-9c5f9d32b01f
2025-07-01 13:56:37 - AgentExecutor - INFO - Single response received for request fd738264-9a5f-48bb-b97f-9c5f9d32b01f.
2025-07-01 13:56:37 - TransitionHandler - INFO - Execution result from agent executor: "[ { \"part\": \"(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)\" }, { \"part\": \"Narrator: \"Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?\"\" }, { \"part\": \"(Close-ups of the iPhone\u2019s minimalist and advanced design.)\\n\\nNarrator: \"Check this out \u2013 the latest iPhone redefines what a smartphone can do! Isn't that wild?\"\" }, { \"part\": \"(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: \"Start your day... like, super smoothly. The new chip launches apps faster than ever\u2014whether you\u2019re keeping up with emails or sharing those killer moments!\"\" }, { \"part\": \"(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: \"Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow.\"\" }, { \"part\": \"(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: \"Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?\"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)\" } ]"
2025-07-01 13:56:37 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:56:37 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition Result received.', 'result': {'data': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'data_type': 'string', 'semantic_type': 'string', 'property_name': 'content'}, 'status': 'completed', 'sequence': 2, 'workflow_status': 'running', 'approval_required': False}
2025-07-01 13:56:37 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in memory: {'AgenticAI': {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}, 'status': 'completed', 'timestamp': 1751358397.958581}}
2025-07-01 13:56:38 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-AgenticAI-*************'
2025-07-01 13:56:39 - RedisManager - DEBUG - Set key 'result:transition-AgenticAI-*************' with TTL of 300 seconds
2025-07-01 13:56:39 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 13:56:39 - StateManager - INFO - Marked transition transition-AgenticAI-************* as completed (was_pending=False, was_waiting=False)
2025-07-01 13:56:39 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-AgenticAI-*************'}
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-AgenticAI-*************
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-AgenticAI-*************:
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-AgenticAI-*************, returning empty list
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-AgenticAI-*************
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 2
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-AgenticAI-*************
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-CombineTextComponent-*************
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-07-01 13:56:39 - TransitionHandler - INFO - Completed transition transition-AgenticAI-************* in 17.29 seconds
2025-07-01 13:56:39 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:56:39 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'result': 'Completed transition in 17.29 seconds', 'message': 'Transition completed in 17.29 seconds', 'transition_id': 'transition-AgenticAI-*************', 'status': 'time_logged', 'sequence': 3, 'workflow_status': 'running'}
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-AgenticAI-*************: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 2
2025-07-01 13:56:39 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']]
2025-07-01 13:56:39 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-*************: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-07-01 13:56:39 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-01 13:56:39 - EnhancedWorkflowEngine - INFO - Transition transition-AgenticAI-************* completed successfully: 2 next transitions
2025-07-01 13:56:39 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-07-01 13:56:39 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-07-01 13:56:39 - EnhancedWorkflowEngine - INFO - Adding transition transition-CombineTextComponent-************* to waiting (dependencies not yet met)
2025-07-01 13:56:39 - EnhancedWorkflowEngine - INFO - Adding transition transition-AgenticAI-************* to pending (all dependencies met)
2025-07-01 13:56:39 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-07-01 13:56:39 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 13:56:39 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe'
2025-07-01 13:56:39 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 13:56:39 - RedisManager - DEBUG - Set key 'workflow_state:f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe' with TTL of 600 seconds
2025-07-01 13:56:39 - StateManager - INFO - Workflow state saved to Redis for workflow ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 13:56:39 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-07-01 13:56:39 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-01 13:56:39 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-07-01 13:56:39 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 13:56:39 - StateManager - INFO - Terminated: False
2025-07-01 13:56:39 - StateManager - INFO - Pending transitions (0): []
2025-07-01 13:56:39 - StateManager - INFO - Waiting transitions (1): ['transition-CombineTextComponent-*************']
2025-07-01 13:56:39 - StateManager - INFO - Completed transitions (1): ['transition-AgenticAI-*************']
2025-07-01 13:56:39 - StateManager - INFO - Results stored for 1 transitions
2025-07-01 13:56:39 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 13:56:39 - StateManager - INFO - Workflow status: active
2025-07-01 13:56:39 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 13:56:39 - StateManager - INFO - Workflow status: active
2025-07-01 13:56:39 - StateManager - INFO - Workflow paused: False
2025-07-01 13:56:39 - StateManager - INFO - ==============================
2025-07-01 13:56:39 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-07-01 13:56:39 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:56:39 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 4, 'workflow_status': 'running'}
2025-07-01 13:56:39 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=standard, execution_type=agent)
2025-07-01 13:56:39 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-07-01 13:56:39 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-07-01 13:56:40 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 13:56:40 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 13:56:40 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 13:56:40 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 13:56:40 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 13:56:40 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 13:56:40 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 13:56:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:56:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:56:40 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:56:40 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:56:40 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:56:40 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:56:40 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-07-01 13:56:40 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-07-01 13:56:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:56:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:56:40 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:56:40 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:56:40 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:56:40 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:56:40 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → query via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:56:40 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-07-01 13:56:40 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-07-01 13:56:40 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-07-01 13:56:40 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with empty collection: {}
2025-07-01 13:56:40 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_type = component
2025-07-01 13:56:40 - TransitionHandler - DEBUG - 📌 Added static parameter: execution_type = response
2025-07-01 13:56:40 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_config = {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}
2025-07-01 13:56:40 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 13:56:40 - TransitionHandler - DEBUG - tool Parameters: {'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 13:56:40 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 13:56:40 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:56:40 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 5, 'workflow_status': 'running'}
2025-07-01 13:56:40 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 5d152592-b35f-4f7f-a1f4-751ea8a1c246) with correlation_id: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-07-01 13:56:40 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-07-01 13:56:40 - AgentExecutor - DEBUG - Added correlation_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe to payload
2025-07-01 13:56:40 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '5d152592-b35f-4f7f-a1f4-751ea8a1c246', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': 'f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe', 'agent_type': 'component', 'execution_type': 'response', 'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'variables': {}, 'agent_config': {'id': '5f77efa0-b04d-49bc-9714-e9241879d002', 'name': 'AI Agent', 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-07-01 13:56:40 - AgentExecutor - DEBUG - Request 5d152592-b35f-4f7f-a1f4-751ea8a1c246 sent successfully using provided producer.
2025-07-01 13:56:40 - AgentExecutor - DEBUG - Waiting for single response result for request 5d152592-b35f-4f7f-a1f4-751ea8a1c246...
2025-07-01 13:56:43 - AgentExecutor - DEBUG - Result consumer received message: Offset=24535
2025-07-01 13:56:43 - AgentExecutor - DEBUG - Processing result payload: {'run_id': '5d152592-b35f-4f7f-a1f4-751ea8a1c246', 'session_id': '5d152592-b35f-4f7f-a1f4-751ea8a1c246', 'event_type': None, 'agent_response': {'content': '0', 'source': 'ai_agent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'TextMessage', 'metadata': {}}, 'success': True, 'message': 'Message processed successfully', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'component', 'request_id': '5d152592-b35f-4f7f-a1f4-751ea8a1c246', 'error_code': None, 'details': None}
2025-07-01 13:56:43 - AgentExecutor - DEBUG - Agent response extracted: {'content': '0', 'source': 'ai_agent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'TextMessage', 'metadata': {}}
2025-07-01 13:56:43 - AgentExecutor - DEBUG - Content extracted: 0...
2025-07-01 13:56:43 - AgentExecutor - DEBUG - Received valid result for request_id 5d152592-b35f-4f7f-a1f4-751ea8a1c246
2025-07-01 13:56:43 - AgentExecutor - INFO - Single response received for request 5d152592-b35f-4f7f-a1f4-751ea8a1c246.
2025-07-01 13:56:43 - TransitionHandler - INFO - Execution result from agent executor: "0"
2025-07-01 13:56:43 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:56:43 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition Result received.', 'result': {'data': '0', 'data_type': 'string', 'semantic_type': 'string', 'property_name': 'content'}, 'status': 'completed', 'sequence': 6, 'workflow_status': 'running', 'approval_required': False}
2025-07-01 13:56:43 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in memory: {'AgenticAI': {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': '0'}, 'status': 'completed', 'timestamp': 1751358403.6094}}
2025-07-01 13:56:44 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-AgenticAI-*************'
2025-07-01 13:56:44 - RedisManager - DEBUG - Set key 'result:transition-AgenticAI-*************' with TTL of 300 seconds
2025-07-01 13:56:44 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 13:56:44 - StateManager - INFO - Marked transition transition-AgenticAI-************* as completed (was_pending=False, was_waiting=False)
2025-07-01 13:56:44 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'transition-AgenticAI-*************', 'transition-AgenticAI-*************'}
2025-07-01 13:56:44 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-AgenticAI-*************
2025-07-01 13:56:44 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-07-01 13:56:44 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-07-01 13:56:44 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-07-01 13:56:44 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-AgenticAI-*************:
2025-07-01 13:56:44 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-01 13:56:44 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-01 13:56:44 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-AgenticAI-*************, returning empty list
2025-07-01 13:56:44 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-AgenticAI-*************
2025-07-01 13:56:44 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-07-01 13:56:44 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-LoopNode-*************']
2025-07-01 13:56:44 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-01 13:56:44 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-01 13:56:44 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-LoopNode-*************
2025-07-01 13:56:44 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-LoopNode-*************']
2025-07-01 13:56:44 - TransitionHandler - INFO - Completed transition transition-AgenticAI-************* in 4.55 seconds
2025-07-01 13:56:44 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:56:44 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'result': 'Completed transition in 4.55 seconds', 'message': 'Transition completed in 4.55 seconds', 'transition_id': 'transition-AgenticAI-*************', 'status': 'time_logged', 'sequence': 7, 'workflow_status': 'running'}
2025-07-01 13:56:44 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-AgenticAI-*************: ['transition-LoopNode-*************']
2025-07-01 13:56:44 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-07-01 13:56:44 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-LoopNode-*************']]
2025-07-01 13:56:44 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-*************: ['transition-LoopNode-*************']
2025-07-01 13:56:44 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-01 13:56:44 - EnhancedWorkflowEngine - INFO - Transition transition-AgenticAI-************* completed successfully: 1 next transitions
2025-07-01 13:56:44 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-LoopNode-*************']
2025-07-01 13:56:44 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-LoopNode-*************']
2025-07-01 13:56:44 - EnhancedWorkflowEngine - INFO - Adding transition transition-LoopNode-************* to pending (all dependencies met)
2025-07-01 13:56:44 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-07-01 13:56:45 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe'
2025-07-01 13:56:45 - RedisManager - DEBUG - Set key 'workflow_state:f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe' with TTL of 600 seconds
2025-07-01 13:56:45 - StateManager - INFO - Workflow state saved to Redis for workflow ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 13:56:45 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-07-01 13:56:45 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-01 13:56:45 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-07-01 13:56:45 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 13:56:45 - StateManager - INFO - Terminated: False
2025-07-01 13:56:45 - StateManager - INFO - Pending transitions (0): []
2025-07-01 13:56:45 - StateManager - INFO - Waiting transitions (1): ['transition-CombineTextComponent-*************']
2025-07-01 13:56:45 - StateManager - INFO - Completed transitions (2): ['transition-AgenticAI-*************', 'transition-AgenticAI-*************']
2025-07-01 13:56:45 - StateManager - INFO - Results stored for 2 transitions
2025-07-01 13:56:45 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 13:56:45 - StateManager - INFO - Workflow status: active
2025-07-01 13:56:45 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 13:56:45 - StateManager - INFO - Workflow status: active
2025-07-01 13:56:45 - StateManager - INFO - Workflow paused: False
2025-07-01 13:56:45 - StateManager - INFO - ==============================
2025-07-01 13:56:45 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-07-01 13:56:45 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:56:45 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 8, 'workflow_status': 'running'}
2025-07-01 13:56:45 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=standard, execution_type=loop)
2025-07-01 13:56:45 - LoopExecutor - DEBUG - 🔗 Orchestration engine reference set in LoopExecutor
2025-07-01 13:56:45 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-07-01 13:56:45 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-07-01 13:56:45 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-07-01 13:56:45 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-07-01 13:56:45 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-07-01 13:56:46 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 13:56:46 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 13:56:46 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 13:56:46 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 13:56:46 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 13:56:46 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '0'}
2025-07-01 13:56:46 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:56:46 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: 0
2025-07-01 13:56:46 - WorkflowUtils - DEBUG - Found result.result: 0 (type: <class 'str'>)
2025-07-01 13:56:46 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:56:46 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:56:46 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-07-01 13:56:46 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-07-01 13:56:46 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '0'}
2025-07-01 13:56:46 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:56:46 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: 0
2025-07-01 13:56:46 - WorkflowUtils - DEBUG - Found result.result: 0 (type: <class 'str'>)
2025-07-01 13:56:46 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:56:46 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:56:46 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → start via path 'result': 0
2025-07-01 13:56:46 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-07-01 13:56:46 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-07-01 13:56:46 - WorkflowUtils - DEBUG - Filtering out field 'iteration_list' with empty collection: []
2025-07-01 13:56:46 - WorkflowUtils - DEBUG - Filtering out field 'start' with null/empty value: None
2025-07-01 13:56:46 - WorkflowUtils - INFO - 🧹 Parameter filtering: 14 → 12 fields (2 null/empty fields removed)
2025-07-01 13:56:46 - TransitionHandler - DEBUG - 📌 Added static parameter: source_type = number_range
2025-07-01 13:56:46 - TransitionHandler - DEBUG - 📌 Added static parameter: batch_size = 1
2025-07-01 13:56:46 - TransitionHandler - DEBUG - 📌 Added static parameter: end = 6
2025-07-01 13:56:46 - TransitionHandler - DEBUG - 📌 Added static parameter: step = 1
2025-07-01 13:56:46 - TransitionHandler - DEBUG - 📌 Added static parameter: parallel_execution = False
2025-07-01 13:56:46 - TransitionHandler - DEBUG - 📌 Added static parameter: max_concurrent = 3
2025-07-01 13:56:46 - TransitionHandler - DEBUG - 📌 Added static parameter: preserve_order = True
2025-07-01 13:56:46 - TransitionHandler - DEBUG - 📌 Added static parameter: iteration_timeout = 60
2025-07-01 13:56:46 - TransitionHandler - DEBUG - 📌 Added static parameter: aggregation_type = collect_all
2025-07-01 13:56:46 - TransitionHandler - DEBUG - 📌 Added static parameter: include_metadata = False
2025-07-01 13:56:46 - TransitionHandler - DEBUG - 📌 Added static parameter: on_iteration_error = retry_once
2025-07-01 13:56:46 - TransitionHandler - DEBUG - 📌 Added static parameter: include_errors = True
2025-07-01 13:56:46 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-07-01 13:56:46 - TransitionHandler - DEBUG - tool Parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-07-01 13:56:46 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-07-01 13:56:46 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:56:46 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 9, 'workflow_status': 'running'}
2025-07-01 13:56:46 - LoopExecutor - INFO - 🔄 Starting loop execution for transition transition-LoopNode-*************
2025-07-01 13:56:46 - LoopExecutor - INFO - 🔧 Resolving loop configuration for transition transition-LoopNode-*************
2025-07-01 13:56:46 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 13:56:46 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 13:56:46 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 13:56:46 - LoopExecutor - DEBUG - 📋 Resolved loop configuration: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 1, 'end': 6}, 'step': 1, 'source_type': 'number_range'}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}}
2025-07-01 13:56:46 - LoopStateManager - INFO - 🔧 Initializing loop state for loop loop_transition-LoopNode-*************_d8d0ae6b
2025-07-01 13:56:46 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_d8d0ae6b, transition_id: transition-LoopNode-*************
2025-07-01 13:56:46 - LoopStateManager - INFO - ✅ Loop state initialized: 6 iterations planned
2025-07-01 13:56:46 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-*************: ['transition-AgenticAI-*************']
2025-07-01 13:56:46 - StateManager - DEBUG - Extracted dependencies for transition transition-LoopNode-*************: ['transition-AgenticAI-*************']
2025-07-01 13:56:46 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 13:56:46 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-1751005477464: ['transition-LoopNode-*************']
2025-07-01 13:56:46 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-*************: ['transition-CombineTextComponent-*************']
2025-07-01 13:56:46 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-*************']
2025-07-01 13:56:46 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-*************']
2025-07-01 13:56:46 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-*************', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 13:56:46 - StateManager - INFO - Built dependency map for 9 transitions
2025-07-01 13:56:46 - StateManager - DEBUG - Transition transition-AgenticAI-************* depends on: ['transition-AgenticAI-*************']
2025-07-01 13:56:46 - StateManager - DEBUG - Transition transition-LoopNode-************* depends on: ['transition-AgenticAI-*************']
2025-07-01 13:56:46 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 13:56:46 - StateManager - DEBUG - Transition transition-MergeDataComponent-1751005477464 depends on: ['transition-LoopNode-*************']
2025-07-01 13:56:46 - StateManager - DEBUG - Transition transition-AgenticAI-************* depends on: ['transition-CombineTextComponent-*************']
2025-07-01 13:56:46 - StateManager - DEBUG - Transition transition-AgenticAI-1751001471087 depends on: ['transition-AgenticAI-*************']
2025-07-01 13:56:46 - StateManager - DEBUG - Transition transition-AgenticAI-1751001474606 depends on: ['transition-AgenticAI-*************']
2025-07-01 13:56:46 - StateManager - DEBUG - Transition transition-CombineTextComponent-1750920624318 depends on: ['transition-AgenticAI-*************', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 13:56:46 - LoopStateManager - INFO - 🔧 Loop body configured - Entry: ['transition-CombineTextComponent-*************'], Exit: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:56:46 - LoopStateManager - DEBUG - 🔗 Loop body dependencies: {'transition-CombineTextComponent-*************': [], 'transition-CombineTextComponent-1750920624318': []}
2025-07-01 13:56:46 - LoopExecutor - INFO - 🔄 Executing iteration 1/6
2025-07-01 13:56:46 - LoopExecutor - INFO - 🔄 Executing iteration 1 with item: 1
2025-07-01 13:56:46 - LoopStateManager - INFO - 🚀 Starting iteration 1/6
2025-07-01 13:56:46 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_d8d0ae6b, transition_id: transition-LoopNode-*************
2025-07-01 13:56:46 - LoopStateManager - DEBUG - 📋 Initialized iteration state - Pending: ['transition-CombineTextComponent-*************'], Waiting: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:56:46 - LoopStateManager - DEBUG - 🔄 Cleared iteration state - Ready for new iteration
2025-07-01 13:56:46 - LoopExecutor - DEBUG - 🔄 Executing pending transitions: ['transition-CombineTextComponent-*************']
2025-07-01 13:56:46 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-07-01 13:56:46 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:56:46 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 10, 'workflow_status': 'running'}
2025-07-01 13:56:46 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-07-01 13:56:46 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 13:56:46 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-07-01 13:56:46 - TransitionHandler - DEBUG - 🔄 Found iteration context for loop execution: {'iteration_index': 0, 'iteration_item': 1, 'loop_id': 'loop_transition-LoopNode-*************_d8d0ae6b', 'transition_id': 'transition-LoopNode-*************'}
2025-07-01 13:56:46 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 13:56:46 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 13:56:47 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 13:56:47 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 13:56:47 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 13:56:47 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 13:56:48 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 13:56:50 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:56:51 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-07-01 13:56:51 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-LoopNode-*************
2025-07-01 13:56:51 - TransitionHandler - DEBUG - 🔄 Adding iteration context for loop transition transition-LoopNode-*************: {'iteration_index': 0, 'iteration_item': 1, 'loop_id': 'loop_transition-LoopNode-*************_d8d0ae6b', 'transition_id': 'transition-LoopNode-*************'}
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 1, 'iteration_index': 0, 'loop_id': 'loop_transition-LoopNode-*************_d8d0ae6b'}
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'loop_id']
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-07-01 13:56:51 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (2/2 compatible)
2025-07-01 13:56:51 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (2/2 compatible)
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 1, 'iteration_index': 0, 'loop_id': 'loop_transition-LoopNode-*************_d8d0ae6b'}
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'loop_id']
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 1
2025-07-01 13:56:51 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/2 successful
2025-07-01 13:56:51 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/2 successful
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: 
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 13:56:51 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 13:56:51 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 13:56:51 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-07-01 13:56:51 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 13:56:51 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 13:56:51 - TransitionHandler - DEBUG - tool Parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 13:56:51 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 13:56:51 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 11, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:56:51 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 11, 'workflow_status': 'running'}
2025-07-01 13:56:51 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 1491ec83-80d0-4486-8ce8-c2293889d304) using provided producer.
2025-07-01 13:56:51 - NodeExecutor - DEBUG - Added correlation_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe to payload
2025-07-01 13:56:51 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-07-01 13:56:51 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '1491ec83-80d0-4486-8ce8-c2293889d304', 'correlation_id': 'f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-07-01 13:56:51 - NodeExecutor - DEBUG - Request 1491ec83-80d0-4486-8ce8-c2293889d304 sent successfully using provided producer.
2025-07-01 13:56:51 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 1491ec83-80d0-4486-8ce8-c2293889d304...
2025-07-01 13:56:51 - AgentExecutor - DEBUG - Result consumer received message: Offset=24536
2025-07-01 13:56:51 - AgentExecutor - DEBUG - Processing result payload: {'run_id': '5d152592-b35f-4f7f-a1f4-751ea8a1c246', 'session_id': '5d152592-b35f-4f7f-a1f4-751ea8a1c246', 'event_type': None, 'agent_response': {'content': 'Error: Connection error.'}, 'success': False, 'message': 'Message processed successfully', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'component', 'request_id': '5d152592-b35f-4f7f-a1f4-751ea8a1c246'}
2025-07-01 13:56:51 - AgentExecutor - WARNING - Received connection error for request 5d152592-b35f-4f7f-a1f4-751ea8a1c246, but continuing to wait for potential success response
2025-07-01 13:56:51 - NodeExecutor - DEBUG - Result consumer received message: Offset=1005
2025-07-01 13:56:51 - NodeExecutor - DEBUG - Received valid result for request_id 1491ec83-80d0-4486-8ce8-c2293889d304
2025-07-01 13:56:51 - NodeExecutor - INFO - Result received for request 1491ec83-80d0-4486-8ce8-c2293889d304.
2025-07-01 13:56:51 - TransitionHandler - INFO - Execution result from Components executor: "1"
2025-07-01 13:56:51 - TransitionHandler - ERROR - Result callback function raised an exception: cannot access local variable 'formatted_result' where it is not associated with a value
2025-07-01 13:56:51 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '1'}, 'status': 'completed', 'timestamp': 1751358411.713068}}
2025-07-01 13:56:52 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-07-01 13:56:52 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-07-01 13:56:52 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 13:56:52 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=True)
2025-07-01 13:56:52 - StateManager - DEBUG - Updated state: pending={'transition-CombineTextComponent-*************_iteration_0'}, waiting=set(), completed={'transition-AgenticAI-*************', 'transition-CombineTextComponent-*************', 'transition-AgenticAI-*************'}
2025-07-01 13:56:52 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-07-01 13:56:52 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-07-01 13:56:52 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-07-01 13:56:52 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-07-01 13:56:52 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-07-01 13:56:52 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-01 13:56:52 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-01 13:56:52 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-07-01 13:56:52 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-07-01 13:56:52 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-07-01 13:56:52 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-AgenticAI-*************']
2025-07-01 13:56:52 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-01 13:56:52 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-01 13:56:52 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-AgenticAI-*************
2025-07-01 13:56:52 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-AgenticAI-*************']
2025-07-01 13:56:52 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 5.57 seconds
2025-07-01 13:56:52 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 12, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:56:52 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'result': 'Completed transition in 5.57 seconds', 'message': 'Transition completed in 5.57 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 12, 'workflow_status': 'running'}
2025-07-01 13:56:52 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-AgenticAI-*************']
2025-07-01 13:56:52 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-07-01 13:56:53 - StateManager - DEBUG - No result found in Redis for transition transition-CombineTextComponent-*************_iteration_0. Trying PostgreSQL next.
2025-07-01 13:56:55 - PostgresManager - DEBUG - No result found for transition transition-CombineTextComponent-*************_iteration_0 in correlation f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:56:55 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-CombineTextComponent-*************_iteration_0. Trying in-memory next.
2025-07-01 13:56:55 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-CombineTextComponent-*************_iteration_0
2025-07-01 13:56:55 - LoopExecutor - DEBUG - ✅ Transition transition-CombineTextComponent-************* completed in iteration 0
2025-07-01 13:56:55 - LoopStateManager - DEBUG - ✅ Marked transition-CombineTextComponent-************* as completed
2025-07-01 13:56:55 - LoopStateManager - DEBUG - 🔓 Newly available transitions: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:56:55 - LoopExecutor - DEBUG - 🔄 Executing pending transitions: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:56:55 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-1750920624318
2025-07-01 13:56:55 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 13, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:56:55 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-1750920624318', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-1750920624318', 'status': 'started', 'sequence': 13, 'workflow_status': 'running'}
2025-07-01 13:56:55 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-1750920624318' (type=standard, execution_type=Components)
2025-07-01 13:56:55 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 13:56:55 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-1750920624318
2025-07-01 13:56:55 - TransitionHandler - DEBUG - 🔄 Found iteration context for loop execution: {'iteration_index': 0, 'iteration_item': 1, 'loop_id': 'loop_transition-LoopNode-*************_d8d0ae6b', 'transition_id': 'transition-LoopNode-*************'}
2025-07-01 13:56:55 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 13:56:55 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 13:56:56 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-*************. Trying PostgreSQL next.
2025-07-01 13:56:58 - PostgresManager - DEBUG - No result found for transition transition-AgenticAI-************* in correlation f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:56:59 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-AgenticAI-*************. Trying in-memory next.
2025-07-01 13:56:59 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-AgenticAI-*************
2025-07-01 13:56:59 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-1751001471087. Trying PostgreSQL next.
2025-07-01 13:57:02 - PostgresManager - DEBUG - No result found for transition transition-AgenticAI-1751001471087 in correlation f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:57:02 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-AgenticAI-1751001471087. Trying in-memory next.
2025-07-01 13:57:02 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-AgenticAI-1751001471087
2025-07-01 13:57:03 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-1751001474606. Trying PostgreSQL next.
2025-07-01 13:57:05 - PostgresManager - DEBUG - No result found for transition transition-AgenticAI-1751001474606 in correlation f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:57:05 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-AgenticAI-1751001474606. Trying in-memory next.
2025-07-01 13:57:05 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-AgenticAI-1751001474606
2025-07-01 13:57:05 - TransitionHandler - DEBUG - 🔄 Adding iteration context for loop transition transition-LoopNode-*************: {'iteration_index': 0, 'iteration_item': 1, 'loop_id': 'loop_transition-LoopNode-*************_d8d0ae6b', 'transition_id': 'transition-LoopNode-*************'}
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-1751001471087 (total: 1)
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-1751001474606 (total: 1)
2025-07-01 13:57:05 - WorkflowUtils - INFO - 🔍 Handle mapping validation: incompatible (0/3 compatible)
2025-07-01 13:57:05 - TransitionHandler - INFO - 🔍 Handle validation: incompatible (0/3 compatible)
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {}
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle final_answer
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 13:57:05 - WorkflowUtils - WARNING - ❌ Handle mapping failed: final_answer → input_2 (no data found)
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {}
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle final_answer
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 13:57:05 - WorkflowUtils - WARNING - ❌ Handle mapping failed: final_answer → input_1 (no data found)
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {}
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle final_answer
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 13:57:05 - WorkflowUtils - WARNING - ❌ Handle mapping failed: final_answer → main_input (no data found)
2025-07-01 13:57:05 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 0/3 successful
2025-07-01 13:57:05 - WorkflowUtils - WARNING - ⚠️ 3 universal handle mappings failed - this may cause tool execution errors
2025-07-01 13:57:05 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 0/3 successful
2025-07-01 13:57:05 - TransitionHandler - WARNING - ❌ Failed mapping: final_answer → input_2 (Error: No data found for handle final_answer)
2025-07-01 13:57:05 - TransitionHandler - WARNING - ❌ Failed mapping: final_answer → input_1 (Error: No data found for handle final_answer)
2025-07-01 13:57:05 - TransitionHandler - WARNING - ❌ Failed mapping: final_answer → main_input (Error: No data found for handle final_answer)
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 13:57:05 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 13:57:05 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 13:57:05 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 2
2025-07-01 13:57:05 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 13:57:05 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'num_additional_inputs': '2', 'separator': '\\n'}
2025-07-01 13:57:05 - TransitionHandler - DEBUG - tool Parameters: {'num_additional_inputs': '2', 'separator': '\\n'}
2025-07-01 13:57:05 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-1750920624318' with parameters: {'num_additional_inputs': '2', 'separator': '\\n'}
2025-07-01 13:57:05 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 14, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:57:05 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'transition_id': 'transition-CombineTextComponent-1750920624318', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 14, 'workflow_status': 'running'}
2025-07-01 13:57:05 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 7f1920a4-9b09-498d-a5b9-06fffaa7b58a) using provided producer.
2025-07-01 13:57:05 - NodeExecutor - DEBUG - Added correlation_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe to payload
2025-07-01 13:57:05 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-1750920624318 to payload
2025-07-01 13:57:05 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'num_additional_inputs': '2', 'separator': '\\n'}, 'request_id': '7f1920a4-9b09-498d-a5b9-06fffaa7b58a', 'correlation_id': 'f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe', 'transition_id': 'transition-CombineTextComponent-1750920624318'}
2025-07-01 13:57:05 - NodeExecutor - DEBUG - Request 7f1920a4-9b09-498d-a5b9-06fffaa7b58a sent successfully using provided producer.
2025-07-01 13:57:05 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 7f1920a4-9b09-498d-a5b9-06fffaa7b58a...
2025-07-01 13:57:10 - NodeExecutor - DEBUG - Result consumer received message: Offset=1006
2025-07-01 13:57:10 - NodeExecutor - WARNING - Received error response for request_id 7f1920a4-9b09-498d-a5b9-06fffaa7b58a: Error combining text for request_id 7f1920a4-9b09-498d-a5b9-06fffaa7b58a: "Required field 'main_input' not found in parameters"
2025-07-01 13:57:10 - NodeExecutor - ERROR - Error during node execution 7f1920a4-9b09-498d-a5b9-06fffaa7b58a: Node execution failed: Error combining text for request_id 7f1920a4-9b09-498d-a5b9-06fffaa7b58a: "Required field 'main_input' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 7f1920a4-9b09-498d-a5b9-06fffaa7b58a: "Required field 'main_input' not found in parameters"
2025-07-01 13:57:10 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-1750920624318': Node execution failed: Error combining text for request_id 7f1920a4-9b09-498d-a5b9-06fffaa7b58a: "Required field 'main_input' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 383, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 7f1920a4-9b09-498d-a5b9-06fffaa7b58a: "Required field 'main_input' not found in parameters"

2025-07-01 13:57:10 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 15, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:57:10 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'transition_id': 'transition-CombineTextComponent-1750920624318', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 7f1920a4-9b09-498d-a5b9-06fffaa7b58a: "Required field \'main_input\' not found in parameters"', 'status': 'failed', 'sequence': 15, 'workflow_status': 'running'}
2025-07-01 13:57:10 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-1750920624318: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 7f1920a4-9b09-498d-a5b9-06fffaa7b58a: "Required field 'main_input' not found in parameters"
2025-07-01 13:57:10 - LoopExecutor - ERROR - ❌ Failed to execute transition transition-CombineTextComponent-1750920624318_iteration_0: Exception in transition transition-CombineTextComponent-1750920624318: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 7f1920a4-9b09-498d-a5b9-06fffaa7b58a: "Required field 'main_input' not found in parameters"
2025-07-01 13:57:10 - LoopExecutor - DEBUG - ✅ Transition transition-CombineTextComponent-1750920624318 completed in iteration 0
2025-07-01 13:57:10 - LoopStateManager - DEBUG - ✅ Marked transition-CombineTextComponent-1750920624318 as completed
2025-07-01 13:57:10 - LoopExecutor - INFO - 🏁 Loop body execution completed for iteration 0
2025-07-01 13:57:10 - LoopStateManager - INFO - ✅ Completing iteration 1/6 with status: completed
2025-07-01 13:57:10 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_d8d0ae6b, transition_id: transition-LoopNode-*************
2025-07-01 13:57:10 - LoopExecutor - INFO - ✅ Iteration 1 completed successfully
2025-07-01 13:57:10 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_d8d0ae6b, transition_id: transition-LoopNode-*************
2025-07-01 13:57:10 - LoopExecutor - INFO - 🔄 Executing iteration 2/6
2025-07-01 13:57:10 - LoopExecutor - INFO - 🔄 Executing iteration 2 with item: 2
2025-07-01 13:57:10 - LoopStateManager - INFO - 🚀 Starting iteration 2/6
2025-07-01 13:57:10 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_d8d0ae6b, transition_id: transition-LoopNode-*************
2025-07-01 13:57:10 - LoopStateManager - DEBUG - 📋 Initialized iteration state - Pending: ['transition-CombineTextComponent-*************'], Waiting: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:57:10 - LoopStateManager - DEBUG - 🔄 Cleared iteration state - Ready for new iteration
2025-07-01 13:57:10 - LoopExecutor - DEBUG - 🔄 Executing pending transitions: ['transition-CombineTextComponent-*************']
2025-07-01 13:57:10 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-07-01 13:57:10 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 16, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:57:10 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 16, 'workflow_status': 'running'}
2025-07-01 13:57:10 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-07-01 13:57:10 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 13:57:10 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-07-01 13:57:10 - TransitionHandler - DEBUG - 🔄 Found iteration context for loop execution: {'iteration_index': 1, 'iteration_item': 2, 'loop_id': 'loop_transition-LoopNode-*************_d8d0ae6b', 'transition_id': 'transition-LoopNode-*************'}
2025-07-01 13:57:10 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 13:57:10 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 13:57:11 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 13:57:11 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 13:57:11 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 13:57:11 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 13:57:12 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 13:57:14 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:57:15 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-07-01 13:57:15 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-LoopNode-*************
2025-07-01 13:57:15 - TransitionHandler - DEBUG - 🔄 Adding iteration context for loop transition transition-LoopNode-*************: {'iteration_index': 1, 'iteration_item': 2, 'loop_id': 'loop_transition-LoopNode-*************_d8d0ae6b', 'transition_id': 'transition-LoopNode-*************'}
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 2, 'iteration_index': 1, 'loop_id': 'loop_transition-LoopNode-*************_d8d0ae6b'}
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'loop_id']
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-07-01 13:57:15 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (2/2 compatible)
2025-07-01 13:57:15 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (2/2 compatible)
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 2, 'iteration_index': 1, 'loop_id': 'loop_transition-LoopNode-*************_d8d0ae6b'}
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'loop_id']
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 2
2025-07-01 13:57:15 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/2 successful
2025-07-01 13:57:15 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/2 successful
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: 
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 13:57:15 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 13:57:15 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 13:57:15 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-07-01 13:57:15 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 13:57:15 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 13:57:15 - TransitionHandler - DEBUG - tool Parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 13:57:15 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 13:57:15 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 17, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:57:15 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 17, 'workflow_status': 'running'}
2025-07-01 13:57:15 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 82ea8e6a-a3b2-4152-b94c-4dc49dcb8bf3) using provided producer.
2025-07-01 13:57:15 - NodeExecutor - DEBUG - Added correlation_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe to payload
2025-07-01 13:57:15 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-07-01 13:57:15 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '82ea8e6a-a3b2-4152-b94c-4dc49dcb8bf3', 'correlation_id': 'f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-07-01 13:57:15 - NodeExecutor - DEBUG - Request 82ea8e6a-a3b2-4152-b94c-4dc49dcb8bf3 sent successfully using provided producer.
2025-07-01 13:57:15 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 82ea8e6a-a3b2-4152-b94c-4dc49dcb8bf3...
2025-07-01 13:57:15 - NodeExecutor - DEBUG - Result consumer received message: Offset=1007
2025-07-01 13:57:15 - NodeExecutor - DEBUG - Received valid result for request_id 82ea8e6a-a3b2-4152-b94c-4dc49dcb8bf3
2025-07-01 13:57:15 - NodeExecutor - INFO - Result received for request 82ea8e6a-a3b2-4152-b94c-4dc49dcb8bf3.
2025-07-01 13:57:15 - TransitionHandler - INFO - Execution result from Components executor: "2"
2025-07-01 13:57:15 - TransitionHandler - ERROR - Result callback function raised an exception: cannot access local variable 'formatted_result' where it is not associated with a value
2025-07-01 13:57:15 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '2'}, 'status': 'completed', 'timestamp': 1751358435.747113}}
2025-07-01 13:57:16 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-07-01 13:57:16 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-07-01 13:57:16 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 13:57:16 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-07-01 13:57:16 - StateManager - DEBUG - Updated state: pending={'transition-CombineTextComponent-*************_iteration_1', 'transition-CombineTextComponent-1750920624318_iteration_0'}, waiting=set(), completed={'transition-AgenticAI-*************', 'transition-CombineTextComponent-*************', 'transition-AgenticAI-*************'}
2025-07-01 13:57:16 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-07-01 13:57:16 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-07-01 13:57:16 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-07-01 13:57:16 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-07-01 13:57:16 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-07-01 13:57:16 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-01 13:57:16 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-01 13:57:16 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-07-01 13:57:16 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-07-01 13:57:16 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-07-01 13:57:16 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-AgenticAI-*************']
2025-07-01 13:57:16 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-01 13:57:16 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-01 13:57:16 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-AgenticAI-*************
2025-07-01 13:57:16 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-AgenticAI-*************']
2025-07-01 13:57:16 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 5.69 seconds
2025-07-01 13:57:16 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 18, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:57:16 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'result': 'Completed transition in 5.69 seconds', 'message': 'Transition completed in 5.69 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 18, 'workflow_status': 'running'}
2025-07-01 13:57:16 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-AgenticAI-*************']
2025-07-01 13:57:16 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-07-01 13:57:17 - StateManager - DEBUG - No result found in Redis for transition transition-CombineTextComponent-*************_iteration_1. Trying PostgreSQL next.
2025-07-01 13:57:19 - PostgresManager - DEBUG - No result found for transition transition-CombineTextComponent-*************_iteration_1 in correlation f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:57:19 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-CombineTextComponent-*************_iteration_1. Trying in-memory next.
2025-07-01 13:57:19 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-CombineTextComponent-*************_iteration_1
2025-07-01 13:57:19 - LoopExecutor - DEBUG - ✅ Transition transition-CombineTextComponent-************* completed in iteration 1
2025-07-01 13:57:19 - LoopStateManager - DEBUG - ✅ Marked transition-CombineTextComponent-************* as completed
2025-07-01 13:57:19 - LoopStateManager - DEBUG - 🔓 Newly available transitions: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:57:19 - LoopExecutor - DEBUG - 🔄 Executing pending transitions: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:57:19 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-1750920624318
2025-07-01 13:57:19 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 19, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:57:19 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-1750920624318', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-1750920624318', 'status': 'started', 'sequence': 19, 'workflow_status': 'running'}
2025-07-01 13:57:19 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-1750920624318' (type=standard, execution_type=Components)
2025-07-01 13:57:19 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 13:57:19 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-1750920624318
2025-07-01 13:57:19 - TransitionHandler - DEBUG - 🔄 Found iteration context for loop execution: {'iteration_index': 1, 'iteration_item': 2, 'loop_id': 'loop_transition-LoopNode-*************_d8d0ae6b', 'transition_id': 'transition-LoopNode-*************'}
2025-07-01 13:57:19 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 13:57:19 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 13:57:20 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-*************. Trying PostgreSQL next.
2025-07-01 13:57:22 - PostgresManager - DEBUG - No result found for transition transition-AgenticAI-************* in correlation f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:57:23 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-AgenticAI-*************. Trying in-memory next.
2025-07-01 13:57:23 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-AgenticAI-*************
2025-07-01 13:57:24 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-1751001471087. Trying PostgreSQL next.
2025-07-01 13:57:26 - PostgresManager - DEBUG - No result found for transition transition-AgenticAI-1751001471087 in correlation f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:57:26 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-AgenticAI-1751001471087. Trying in-memory next.
2025-07-01 13:57:26 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-AgenticAI-1751001471087
2025-07-01 13:57:27 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-1751001474606. Trying PostgreSQL next.
2025-07-01 13:57:29 - PostgresManager - DEBUG - No result found for transition transition-AgenticAI-1751001474606 in correlation f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:57:29 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-AgenticAI-1751001474606. Trying in-memory next.
2025-07-01 13:57:29 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-AgenticAI-1751001474606
2025-07-01 13:57:29 - TransitionHandler - DEBUG - 🔄 Adding iteration context for loop transition transition-LoopNode-*************: {'iteration_index': 1, 'iteration_item': 2, 'loop_id': 'loop_transition-LoopNode-*************_d8d0ae6b', 'transition_id': 'transition-LoopNode-*************'}
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-1751001471087 (total: 1)
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-1751001474606 (total: 1)
2025-07-01 13:57:29 - WorkflowUtils - INFO - 🔍 Handle mapping validation: incompatible (0/3 compatible)
2025-07-01 13:57:29 - TransitionHandler - INFO - 🔍 Handle validation: incompatible (0/3 compatible)
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {}
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle final_answer
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 13:57:29 - WorkflowUtils - WARNING - ❌ Handle mapping failed: final_answer → input_2 (no data found)
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {}
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle final_answer
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 13:57:29 - WorkflowUtils - WARNING - ❌ Handle mapping failed: final_answer → input_1 (no data found)
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {}
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle final_answer
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 13:57:29 - WorkflowUtils - WARNING - ❌ Handle mapping failed: final_answer → main_input (no data found)
2025-07-01 13:57:29 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 0/3 successful
2025-07-01 13:57:29 - WorkflowUtils - WARNING - ⚠️ 3 universal handle mappings failed - this may cause tool execution errors
2025-07-01 13:57:29 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 0/3 successful
2025-07-01 13:57:29 - TransitionHandler - WARNING - ❌ Failed mapping: final_answer → input_2 (Error: No data found for handle final_answer)
2025-07-01 13:57:29 - TransitionHandler - WARNING - ❌ Failed mapping: final_answer → input_1 (Error: No data found for handle final_answer)
2025-07-01 13:57:29 - TransitionHandler - WARNING - ❌ Failed mapping: final_answer → main_input (Error: No data found for handle final_answer)
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 13:57:29 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 13:57:29 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 13:57:29 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 2
2025-07-01 13:57:29 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 13:57:29 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'num_additional_inputs': '2', 'separator': '\\n'}
2025-07-01 13:57:29 - TransitionHandler - DEBUG - tool Parameters: {'num_additional_inputs': '2', 'separator': '\\n'}
2025-07-01 13:57:29 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-1750920624318' with parameters: {'num_additional_inputs': '2', 'separator': '\\n'}
2025-07-01 13:57:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 20, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:57:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'transition_id': 'transition-CombineTextComponent-1750920624318', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 20, 'workflow_status': 'running'}
2025-07-01 13:57:29 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 5c027058-c492-4565-aa52-245ac227a1d5) using provided producer.
2025-07-01 13:57:29 - NodeExecutor - DEBUG - Added correlation_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe to payload
2025-07-01 13:57:29 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-1750920624318 to payload
2025-07-01 13:57:29 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'num_additional_inputs': '2', 'separator': '\\n'}, 'request_id': '5c027058-c492-4565-aa52-245ac227a1d5', 'correlation_id': 'f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe', 'transition_id': 'transition-CombineTextComponent-1750920624318'}
2025-07-01 13:57:29 - NodeExecutor - DEBUG - Request 5c027058-c492-4565-aa52-245ac227a1d5 sent successfully using provided producer.
2025-07-01 13:57:29 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 5c027058-c492-4565-aa52-245ac227a1d5...
2025-07-01 13:57:30 - NodeExecutor - DEBUG - Result consumer received message: Offset=1008
2025-07-01 13:57:30 - NodeExecutor - WARNING - Received error response for request_id 5c027058-c492-4565-aa52-245ac227a1d5: Error combining text for request_id 5c027058-c492-4565-aa52-245ac227a1d5: "Required field 'main_input' not found in parameters"
2025-07-01 13:57:30 - NodeExecutor - ERROR - Error during node execution 5c027058-c492-4565-aa52-245ac227a1d5: Node execution failed: Error combining text for request_id 5c027058-c492-4565-aa52-245ac227a1d5: "Required field 'main_input' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 5c027058-c492-4565-aa52-245ac227a1d5: "Required field 'main_input' not found in parameters"
2025-07-01 13:57:30 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-1750920624318': Node execution failed: Error combining text for request_id 5c027058-c492-4565-aa52-245ac227a1d5: "Required field 'main_input' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 383, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 5c027058-c492-4565-aa52-245ac227a1d5: "Required field 'main_input' not found in parameters"

2025-07-01 13:57:30 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 21, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:57:30 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'transition_id': 'transition-CombineTextComponent-1750920624318', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 5c027058-c492-4565-aa52-245ac227a1d5: "Required field \'main_input\' not found in parameters"', 'status': 'failed', 'sequence': 21, 'workflow_status': 'running'}
2025-07-01 13:57:30 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-1750920624318: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 5c027058-c492-4565-aa52-245ac227a1d5: "Required field 'main_input' not found in parameters"
2025-07-01 13:57:30 - LoopExecutor - ERROR - ❌ Failed to execute transition transition-CombineTextComponent-1750920624318_iteration_1: Exception in transition transition-CombineTextComponent-1750920624318: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 5c027058-c492-4565-aa52-245ac227a1d5: "Required field 'main_input' not found in parameters"
2025-07-01 13:57:30 - LoopExecutor - DEBUG - ✅ Transition transition-CombineTextComponent-1750920624318 completed in iteration 1
2025-07-01 13:57:30 - LoopStateManager - DEBUG - ✅ Marked transition-CombineTextComponent-1750920624318 as completed
2025-07-01 13:57:30 - LoopExecutor - INFO - 🏁 Loop body execution completed for iteration 1
2025-07-01 13:57:30 - LoopStateManager - INFO - ✅ Completing iteration 2/6 with status: completed
2025-07-01 13:57:30 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_d8d0ae6b, transition_id: transition-LoopNode-*************
2025-07-01 13:57:30 - LoopExecutor - INFO - ✅ Iteration 2 completed successfully
2025-07-01 13:57:30 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_d8d0ae6b, transition_id: transition-LoopNode-*************
2025-07-01 13:57:30 - LoopExecutor - INFO - 🔄 Executing iteration 3/6
2025-07-01 13:57:30 - LoopExecutor - INFO - 🔄 Executing iteration 3 with item: 3
2025-07-01 13:57:30 - LoopStateManager - INFO - 🚀 Starting iteration 3/6
2025-07-01 13:57:30 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_d8d0ae6b, transition_id: transition-LoopNode-*************
2025-07-01 13:57:30 - LoopStateManager - DEBUG - 📋 Initialized iteration state - Pending: ['transition-CombineTextComponent-*************'], Waiting: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:57:30 - LoopStateManager - DEBUG - 🔄 Cleared iteration state - Ready for new iteration
2025-07-01 13:57:30 - LoopExecutor - DEBUG - 🔄 Executing pending transitions: ['transition-CombineTextComponent-*************']
2025-07-01 13:57:30 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-07-01 13:57:30 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 22, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:57:30 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 22, 'workflow_status': 'running'}
2025-07-01 13:57:30 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-07-01 13:57:30 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 13:57:30 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-07-01 13:57:30 - TransitionHandler - DEBUG - 🔄 Found iteration context for loop execution: {'iteration_index': 2, 'iteration_item': 3, 'loop_id': 'loop_transition-LoopNode-*************_d8d0ae6b', 'transition_id': 'transition-LoopNode-*************'}
2025-07-01 13:57:30 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 13:57:30 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 13:57:31 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 13:57:31 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 13:57:31 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 13:57:31 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 13:57:32 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 13:57:34 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:57:34 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-07-01 13:57:34 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-LoopNode-*************
2025-07-01 13:57:34 - TransitionHandler - DEBUG - 🔄 Adding iteration context for loop transition transition-LoopNode-*************: {'iteration_index': 2, 'iteration_item': 3, 'loop_id': 'loop_transition-LoopNode-*************_d8d0ae6b', 'transition_id': 'transition-LoopNode-*************'}
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 3, 'iteration_index': 2, 'loop_id': 'loop_transition-LoopNode-*************_d8d0ae6b'}
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'loop_id']
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-07-01 13:57:34 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (2/2 compatible)
2025-07-01 13:57:34 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (2/2 compatible)
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 3, 'iteration_index': 2, 'loop_id': 'loop_transition-LoopNode-*************_d8d0ae6b'}
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'loop_id']
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 3
2025-07-01 13:57:34 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/2 successful
2025-07-01 13:57:34 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/2 successful
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: 
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 13:57:34 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 13:57:34 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 13:57:34 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-07-01 13:57:34 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 13:57:34 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'main_input': 3, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 13:57:34 - TransitionHandler - DEBUG - tool Parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'main_input': 3, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 13:57:34 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'main_input': 3, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 13:57:34 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 23, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:57:34 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 23, 'workflow_status': 'running'}
2025-07-01 13:57:34 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 913530dd-1fa1-4df6-83a3-ce5b6e87d7ea) using provided producer.
2025-07-01 13:57:34 - NodeExecutor - DEBUG - Added correlation_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe to payload
2025-07-01 13:57:34 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-07-01 13:57:34 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'main_input': 3, 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '913530dd-1fa1-4df6-83a3-ce5b6e87d7ea', 'correlation_id': 'f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-07-01 13:57:34 - NodeExecutor - DEBUG - Request 913530dd-1fa1-4df6-83a3-ce5b6e87d7ea sent successfully using provided producer.
2025-07-01 13:57:34 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 913530dd-1fa1-4df6-83a3-ce5b6e87d7ea...
2025-07-01 13:57:39 - NodeExecutor - DEBUG - Result consumer received message: Offset=1009
2025-07-01 13:57:39 - NodeExecutor - DEBUG - Received valid result for request_id 913530dd-1fa1-4df6-83a3-ce5b6e87d7ea
2025-07-01 13:57:39 - NodeExecutor - INFO - Result received for request 913530dd-1fa1-4df6-83a3-ce5b6e87d7ea.
2025-07-01 13:57:39 - TransitionHandler - INFO - Execution result from Components executor: "3"
2025-07-01 13:57:39 - TransitionHandler - ERROR - Result callback function raised an exception: cannot access local variable 'formatted_result' where it is not associated with a value
2025-07-01 13:57:39 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '3'}, 'status': 'completed', 'timestamp': 1751358459.1200671}}
2025-07-01 13:57:39 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 13:57:39 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-07-01 13:57:39 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 13:57:39 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-07-01 13:57:39 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 13:57:39 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-07-01 13:57:39 - StateManager - DEBUG - Updated state: pending={'transition-CombineTextComponent-*************_iteration_2', 'transition-CombineTextComponent-1750920624318_iteration_0', 'transition-CombineTextComponent-1750920624318_iteration_1'}, waiting=set(), completed={'transition-AgenticAI-*************', 'transition-CombineTextComponent-*************', 'transition-AgenticAI-*************'}
2025-07-01 13:57:39 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-07-01 13:57:39 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-07-01 13:57:39 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-07-01 13:57:39 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-07-01 13:57:39 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-07-01 13:57:39 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-01 13:57:39 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-01 13:57:39 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-07-01 13:57:39 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-07-01 13:57:39 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-07-01 13:57:39 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-AgenticAI-*************']
2025-07-01 13:57:39 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-01 13:57:39 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-01 13:57:39 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-AgenticAI-*************
2025-07-01 13:57:39 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-AgenticAI-*************']
2025-07-01 13:57:39 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 9.63 seconds
2025-07-01 13:57:39 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 24, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:57:39 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'result': 'Completed transition in 9.63 seconds', 'message': 'Transition completed in 9.63 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 24, 'workflow_status': 'running'}
2025-07-01 13:57:39 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-AgenticAI-*************']
2025-07-01 13:57:39 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-07-01 13:57:40 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 13:57:40 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 13:57:40 - StateManager - DEBUG - No result found in Redis for transition transition-CombineTextComponent-*************_iteration_2. Trying PostgreSQL next.
2025-07-01 13:57:42 - PostgresManager - DEBUG - No result found for transition transition-CombineTextComponent-*************_iteration_2 in correlation f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:57:43 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-CombineTextComponent-*************_iteration_2. Trying in-memory next.
2025-07-01 13:57:43 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-CombineTextComponent-*************_iteration_2
2025-07-01 13:57:43 - LoopExecutor - DEBUG - ✅ Transition transition-CombineTextComponent-************* completed in iteration 2
2025-07-01 13:57:43 - LoopStateManager - DEBUG - ✅ Marked transition-CombineTextComponent-************* as completed
2025-07-01 13:57:43 - LoopStateManager - DEBUG - 🔓 Newly available transitions: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:57:43 - LoopExecutor - DEBUG - 🔄 Executing pending transitions: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:57:43 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-1750920624318
2025-07-01 13:57:43 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 25, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:57:43 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-1750920624318', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-1750920624318', 'status': 'started', 'sequence': 25, 'workflow_status': 'running'}
2025-07-01 13:57:43 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-1750920624318' (type=standard, execution_type=Components)
2025-07-01 13:57:43 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 13:57:43 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-1750920624318
2025-07-01 13:57:43 - TransitionHandler - DEBUG - 🔄 Found iteration context for loop execution: {'iteration_index': 2, 'iteration_item': 3, 'loop_id': 'loop_transition-LoopNode-*************_d8d0ae6b', 'transition_id': 'transition-LoopNode-*************'}
2025-07-01 13:57:43 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 13:57:43 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 13:57:44 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-*************. Trying PostgreSQL next.
2025-07-01 13:57:46 - PostgresManager - DEBUG - No result found for transition transition-AgenticAI-************* in correlation f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:57:46 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-AgenticAI-*************. Trying in-memory next.
2025-07-01 13:57:46 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-AgenticAI-*************
2025-07-01 13:57:47 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-1751001471087. Trying PostgreSQL next.
2025-07-01 13:57:49 - PostgresManager - DEBUG - No result found for transition transition-AgenticAI-1751001471087 in correlation f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:57:49 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-AgenticAI-1751001471087. Trying in-memory next.
2025-07-01 13:57:49 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-AgenticAI-1751001471087
2025-07-01 13:57:50 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-1751001474606. Trying PostgreSQL next.
2025-07-01 13:57:52 - PostgresManager - DEBUG - No result found for transition transition-AgenticAI-1751001474606 in correlation f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe
2025-07-01 13:57:53 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-AgenticAI-1751001474606. Trying in-memory next.
2025-07-01 13:57:53 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-AgenticAI-1751001474606
2025-07-01 13:57:53 - TransitionHandler - DEBUG - 🔄 Adding iteration context for loop transition transition-LoopNode-*************: {'iteration_index': 2, 'iteration_item': 3, 'loop_id': 'loop_transition-LoopNode-*************_d8d0ae6b', 'transition_id': 'transition-LoopNode-*************'}
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-1751001471087 (total: 1)
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-1751001474606 (total: 1)
2025-07-01 13:57:53 - WorkflowUtils - INFO - 🔍 Handle mapping validation: incompatible (0/3 compatible)
2025-07-01 13:57:53 - TransitionHandler - INFO - 🔍 Handle validation: incompatible (0/3 compatible)
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {}
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle final_answer
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 13:57:53 - WorkflowUtils - WARNING - ❌ Handle mapping failed: final_answer → input_2 (no data found)
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {}
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle final_answer
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 13:57:53 - WorkflowUtils - WARNING - ❌ Handle mapping failed: final_answer → input_1 (no data found)
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {}
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle final_answer
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 13:57:53 - WorkflowUtils - WARNING - ❌ Handle mapping failed: final_answer → main_input (no data found)
2025-07-01 13:57:53 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 0/3 successful
2025-07-01 13:57:53 - WorkflowUtils - WARNING - ⚠️ 3 universal handle mappings failed - this may cause tool execution errors
2025-07-01 13:57:53 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 0/3 successful
2025-07-01 13:57:53 - TransitionHandler - WARNING - ❌ Failed mapping: final_answer → input_2 (Error: No data found for handle final_answer)
2025-07-01 13:57:53 - TransitionHandler - WARNING - ❌ Failed mapping: final_answer → input_1 (Error: No data found for handle final_answer)
2025-07-01 13:57:53 - TransitionHandler - WARNING - ❌ Failed mapping: final_answer → main_input (Error: No data found for handle final_answer)
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 13:57:53 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 13:57:53 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 13:57:53 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 2
2025-07-01 13:57:53 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 13:57:53 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'num_additional_inputs': '2', 'separator': '\\n'}
2025-07-01 13:57:53 - TransitionHandler - DEBUG - tool Parameters: {'num_additional_inputs': '2', 'separator': '\\n'}
2025-07-01 13:57:53 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-1750920624318' with parameters: {'num_additional_inputs': '2', 'separator': '\\n'}
2025-07-01 13:57:53 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 26, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:57:53 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'transition_id': 'transition-CombineTextComponent-1750920624318', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 26, 'workflow_status': 'running'}
2025-07-01 13:57:53 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: f00de193-f1cd-4c52-a5ad-6cbd93f2260b) using provided producer.
2025-07-01 13:57:53 - NodeExecutor - DEBUG - Added correlation_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe to payload
2025-07-01 13:57:53 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-1750920624318 to payload
2025-07-01 13:57:53 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'num_additional_inputs': '2', 'separator': '\\n'}, 'request_id': 'f00de193-f1cd-4c52-a5ad-6cbd93f2260b', 'correlation_id': 'f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe', 'transition_id': 'transition-CombineTextComponent-1750920624318'}
2025-07-01 13:57:53 - NodeExecutor - DEBUG - Request f00de193-f1cd-4c52-a5ad-6cbd93f2260b sent successfully using provided producer.
2025-07-01 13:57:53 - NodeExecutor - DEBUG - Waiting indefinitely for result for request f00de193-f1cd-4c52-a5ad-6cbd93f2260b...
2025-07-01 13:57:58 - NodeExecutor - DEBUG - Result consumer received message: Offset=1010
2025-07-01 13:57:58 - NodeExecutor - WARNING - Received error response for request_id f00de193-f1cd-4c52-a5ad-6cbd93f2260b: Error combining text for request_id f00de193-f1cd-4c52-a5ad-6cbd93f2260b: "Required field 'main_input' not found in parameters"
2025-07-01 13:57:58 - NodeExecutor - ERROR - Error during node execution f00de193-f1cd-4c52-a5ad-6cbd93f2260b: Node execution failed: Error combining text for request_id f00de193-f1cd-4c52-a5ad-6cbd93f2260b: "Required field 'main_input' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id f00de193-f1cd-4c52-a5ad-6cbd93f2260b: "Required field 'main_input' not found in parameters"
2025-07-01 13:57:58 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-1750920624318': Node execution failed: Error combining text for request_id f00de193-f1cd-4c52-a5ad-6cbd93f2260b: "Required field 'main_input' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 383, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id f00de193-f1cd-4c52-a5ad-6cbd93f2260b: "Required field 'main_input' not found in parameters"

2025-07-01 13:57:58 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 27, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:57:58 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'transition_id': 'transition-CombineTextComponent-1750920624318', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id f00de193-f1cd-4c52-a5ad-6cbd93f2260b: "Required field \'main_input\' not found in parameters"', 'status': 'failed', 'sequence': 27, 'workflow_status': 'running'}
2025-07-01 13:57:58 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-1750920624318: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id f00de193-f1cd-4c52-a5ad-6cbd93f2260b: "Required field 'main_input' not found in parameters"
2025-07-01 13:57:58 - LoopExecutor - ERROR - ❌ Failed to execute transition transition-CombineTextComponent-1750920624318_iteration_2: Exception in transition transition-CombineTextComponent-1750920624318: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id f00de193-f1cd-4c52-a5ad-6cbd93f2260b: "Required field 'main_input' not found in parameters"
2025-07-01 13:57:58 - LoopExecutor - DEBUG - ✅ Transition transition-CombineTextComponent-1750920624318 completed in iteration 2
2025-07-01 13:57:58 - LoopStateManager - DEBUG - ✅ Marked transition-CombineTextComponent-1750920624318 as completed
2025-07-01 13:57:58 - LoopExecutor - INFO - 🏁 Loop body execution completed for iteration 2
2025-07-01 13:57:58 - LoopStateManager - INFO - ✅ Completing iteration 3/6 with status: completed
2025-07-01 13:57:58 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_d8d0ae6b, transition_id: transition-LoopNode-*************
2025-07-01 13:57:58 - LoopExecutor - INFO - ✅ Iteration 3 completed successfully
2025-07-01 13:57:58 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_d8d0ae6b, transition_id: transition-LoopNode-*************
2025-07-01 13:57:58 - LoopExecutor - INFO - 🔄 Executing iteration 4/6
2025-07-01 13:57:58 - LoopExecutor - INFO - 🔄 Executing iteration 4 with item: 4
2025-07-01 13:57:58 - LoopStateManager - INFO - 🚀 Starting iteration 4/6
2025-07-01 13:57:58 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_d8d0ae6b, transition_id: transition-LoopNode-*************
2025-07-01 13:57:58 - LoopStateManager - DEBUG - 📋 Initialized iteration state - Pending: ['transition-CombineTextComponent-*************'], Waiting: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:57:58 - LoopStateManager - DEBUG - 🔄 Cleared iteration state - Ready for new iteration
2025-07-01 13:57:58 - LoopExecutor - DEBUG - 🔄 Executing pending transitions: ['transition-CombineTextComponent-*************']
2025-07-01 13:57:58 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-07-01 13:57:58 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 28, corr_id f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe):
2025-07-01 13:57:58 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 28, 'workflow_status': 'running'}
2025-07-01 13:57:58 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-07-01 13:57:58 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 13:57:58 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-07-01 13:57:58 - TransitionHandler - DEBUG - 🔄 Found iteration context for loop execution: {'iteration_index': 3, 'iteration_item': 4, 'loop_id': 'loop_transition-LoopNode-*************_d8d0ae6b', 'transition_id': 'transition-LoopNode-*************'}
2025-07-01 13:57:58 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 13:57:58 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 13:57:59 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 13:57:59 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 13:57:59 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 13:57:59 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 13:58:00 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 13:58:01 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-07-01 13:58:01 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-07-01 13:58:01 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-07-01 13:58:01 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-07-01 13:58:01 - EnhancedWorkflowEngine - WARNING - Workflow f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe execution was cancelled!
2025-07-01 13:58:01 - StateManager - INFO - Workflow terminated flag set to: True
2025-07-01 13:58:01 - KafkaWorkflowConsumer - WARNING - Workflow execution for '0c19c070-905e-46ef-9f57-62eb427bf396' was cancelled
2025-07-01 13:58:01 - KafkaWorkflowConsumer - INFO - Workflow '0c19c070-905e-46ef-9f57-62eb427bf396' final status: cancelled, result: Workflow '0c19c070-905e-46ef-9f57-62eb427bf396' execution was cancelled
2025-07-01 13:58:01 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe, response: {'status': 'Workflow Cancelled', 'result': "Workflow '0c19c070-905e-46ef-9f57-62eb427bf396' execution was cancelled", 'workflow_status': 'cancelled'}
2025-07-01 13:58:01 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: f0afd42c-77ed-46a7-bb0d-20dfeb6e6bfe 
2025-07-01 13:58:01 - Main - ERROR - Shutting down due to keyboard interrupt...
