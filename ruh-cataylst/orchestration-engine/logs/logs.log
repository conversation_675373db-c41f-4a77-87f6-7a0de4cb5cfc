2025-07-01 12:28:13 - Main - INFO - Starting Server
2025-07-01 12:28:13 - Main - INFO - Connection at: **************:9092
2025-07-01 12:28:13 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-01 12:28:13 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-07-01 12:28:13 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-01 12:28:13 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-07-01 12:28:13 - <PERSON><PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 12:28:15 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 12:28:15 - Red<PERSON>Manager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
