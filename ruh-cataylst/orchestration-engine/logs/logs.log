2025-07-01 16:06:01 - Main - INFO - Starting Server
2025-07-01 16:06:01 - Main - INFO - Connection at: **************:9092
2025-07-01 16:06:01 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-01 16:06:01 - NodeExecutor - INFO - NodeExecutor initialized.
2025-07-01 16:06:01 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-01 16:06:01 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-07-01 16:06:01 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 16:06:04 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 16:06:04 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 16:06:06 - Red<PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 16:06:08 - PostgresManager - INFO - PostgreSQL connection pool created
2025-07-01 16:06:08 - PostgresManager - INFO - PostgreSQL connection pool is available
