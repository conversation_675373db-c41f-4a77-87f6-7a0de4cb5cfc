2025-07-01 12:40:55 - Main - INFO - Starting Server
2025-07-01 12:40:55 - Main - INFO - Connection at: **************:9092
2025-07-01 12:40:55 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-01 12:40:55 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-07-01 12:40:55 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-01 12:40:55 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-07-01 12:40:55 - <PERSON><PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 12:40:56 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 12:40:56 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
