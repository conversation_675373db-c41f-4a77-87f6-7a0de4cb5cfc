2025-07-01 13:27:14 - Main - INFO - Starting Server
2025-07-01 13:27:14 - Main - INFO - Connection at: **************:9092
2025-07-01 13:27:14 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-01 13:27:14 - NodeExecutor - INFO - NodeExecutor initialized.
2025-07-01 13:27:14 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-01 13:27:14 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-07-01 13:27:14 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 13:27:16 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 13:27:16 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 13:27:18 - <PERSON><PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 13:27:20 - PostgresManager - INFO - PostgreSQL connection pool created
2025-07-01 13:27:20 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-07-01 13:27:23 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-07-01 13:27:23 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-07-01 13:27:23 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 13:27:25 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 13:27:25 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 13:27:27 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 13:27:27 - RedisEventListener - INFO - Starting Redis event listener thread
2025-07-01 13:27:27 - RedisEventListener - INFO - Redis event listener started
2025-07-01 13:27:27 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-07-01 13:27:27 - StateManager - DEBUG - Using provided database connections
2025-07-01 13:27:27 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 13:27:27 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 13:27:27 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 13:27:27 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-07-01 13:27:28 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 13:27:28 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 13:27:28 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-07-01 13:27:28 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-07-01 13:27:28 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-07-01 13:27:28 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-07-01 13:27:30 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-07-01 13:27:30 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-07-01 13:27:30 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-07-01 13:27:35 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-07-01 13:27:42 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-07-01 13:27:42 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-07-01 13:27:42 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-07-01 13:27:48 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-07-01 13:27:48 - NodeExecutor - INFO - Background result consumer loop started.
2025-07-01 13:27:48 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-07-01 13:27:54 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-07-01 13:27:54 - AgentExecutor - INFO - Background result consumer loop started.
2025-07-01 13:28:23 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 13:28:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 13:28:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 13:28:24 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 13:29:23 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 13:29:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 13:29:25 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 13:29:25 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 13:30:23 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 13:30:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 13:30:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 13:30:24 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 13:30:55 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1137
2025-07-01 13:30:55 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751356854, 'task_type': 'workflow', 'data': {'workflow_id': '0c19c070-905e-46ef-9f57-62eb427bf396', 'payload': {'user_dependent_fields': ['query'], 'user_payload_template': {'query': {'value': 'nature 101', 'transition_id': 'AgenticAI-*************'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-07-01 13:30:55 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: 0c19c070-905e-46ef-9f57-62eb427bf396
2025-07-01 13:30:55 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/0c19c070-905e-46ef-9f57-62eb427bf396
2025-07-01 13:30:59 - WorkflowService - DEBUG - Received response with status code: 200
2025-07-01 13:30:59 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Ruh_Video_Generation retrieved successfully",
  "workflow": {
    "id": "0c19c070-905e-46ef-9f57-62eb427bf396",
    "name": "Ruh_Video_Generation",
    "description": "Ruh_Video_Generation",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/e2c5a041-2b84-4b33-be90-3d1e07e44353.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/451d0c28-d066-4833-afad-080c37e7563e.json",
    "start_nodes": [
      {
        "field": "query",
        "type": "string",
        "transition_id": "transition-AgenticAI-*************"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": "8d4b3f88-71ea-48dd-b8a2-dc101d22bf8a",
    "template_owner_id": "180c7469-1db0-4707-bc17-eeaa5e7ff64d",
    "is_imported": true,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-29T08:29:44.634200",
    "updated_at": "2025-06-30T08:40:55.500303",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-1751005477464"
      },
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-1750920624318"
      }
    ],
    "is_updated": true
  }
}
2025-07-01 13:30:59 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 0c19c070-905e-46ef-9f57-62eb427bf396 - server_script_path is optional
2025-07-01 13:30:59 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-07-01 13:30:59 - StateManager - DEBUG - Using global database connections from initializer
2025-07-01 13:30:59 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 13:30:59 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 13:30:59 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 13:31:00 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 13:31:00 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 13:31:00 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-07-01 13:31:00 - StateManager - DEBUG - Using provided database connections
2025-07-01 13:31:00 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 13:31:00 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 13:31:00 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 13:31:01 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 13:31:01 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 13:31:01 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-*************: ['transition-AgenticAI-*************']
2025-07-01 13:31:01 - StateManager - DEBUG - Extracted dependencies for transition transition-LoopNode-*************: ['transition-AgenticAI-*************']
2025-07-01 13:31:01 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-1751005477464: ['transition-LoopNode-*************']
2025-07-01 13:31:01 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 13:31:01 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-*************']
2025-07-01 13:31:01 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-07-01 13:31:01 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-07-01 13:31:01 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 13:31:01 - StateManager - INFO - Built dependency map for 9 transitions
2025-07-01 13:31:01 - StateManager - DEBUG - Transition transition-AgenticAI-************* depends on: ['transition-AgenticAI-*************']
2025-07-01 13:31:01 - StateManager - DEBUG - Transition transition-LoopNode-************* depends on: ['transition-AgenticAI-*************']
2025-07-01 13:31:01 - StateManager - DEBUG - Transition transition-MergeDataComponent-1751005477464 depends on: ['transition-LoopNode-*************']
2025-07-01 13:31:01 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 13:31:01 - StateManager - DEBUG - Transition transition-AgenticAI-1750505490787 depends on: ['transition-CombineTextComponent-*************']
2025-07-01 13:31:01 - StateManager - DEBUG - Transition transition-AgenticAI-1751001474606 depends on: ['transition-AgenticAI-1750505490787']
2025-07-01 13:31:01 - StateManager - DEBUG - Transition transition-AgenticAI-1751001471087 depends on: ['transition-AgenticAI-1750505490787']
2025-07-01 13:31:01 - StateManager - DEBUG - Transition transition-CombineTextComponent-1750920624318 depends on: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 13:31:01 - MCPToolExecutor - DEBUG - Set correlation ID to: f08b4c6e-ddd2-4ab0-8898-f69860f935da
2025-07-01 13:31:01 - EnhancedWorkflowEngine - DEBUG - Set correlation_id f08b4c6e-ddd2-4ab0-8898-f69860f935da in tool_executor
2025-07-01 13:31:01 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-07-01 13:31:01 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-07-01 13:31:01 - NodeExecutor - DEBUG - Set correlation ID to: f08b4c6e-ddd2-4ab0-8898-f69860f935da
2025-07-01 13:31:01 - EnhancedWorkflowEngine - DEBUG - Set correlation_id f08b4c6e-ddd2-4ab0-8898-f69860f935da in node_executor
2025-07-01 13:31:01 - AgentExecutor - DEBUG - Set correlation ID to: f08b4c6e-ddd2-4ab0-8898-f69860f935da
2025-07-01 13:31:01 - EnhancedWorkflowEngine - DEBUG - Set correlation_id f08b4c6e-ddd2-4ab0-8898-f69860f935da in agent_executor
2025-07-01 13:31:01 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-07-01 13:31:01 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-07-01 13:31:01 - TransitionHandler - INFO - TransitionHandler initialized
2025-07-01 13:31:01 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da
2025-07-01 13:31:01 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: f08b4c6e-ddd2-4ab0-8898-f69860f935da
2025-07-01 13:31:01 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-07-01 13:31:01 - StateManager - INFO - Workflow initialized with initial transition: transition-AgenticAI-*************
2025-07-01 13:31:01 - StateManager - DEBUG - State: pending={'transition-AgenticAI-*************'}, waiting=set(), completed=set()
2025-07-01 13:31:01 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-AgenticAI-*************
2025-07-01 13:31:01 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-07-01 13:31:02 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:f08b4c6e-ddd2-4ab0-8898-f69860f935da'
2025-07-01 13:31:02 - RedisManager - DEBUG - Set key 'workflow_state:f08b4c6e-ddd2-4ab0-8898-f69860f935da' with TTL of 600 seconds
2025-07-01 13:31:02 - StateManager - INFO - Workflow state saved to Redis for workflow ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 13:31:02 - StateManager - DEBUG - Checking waiting transitions: set()
2025-07-01 13:31:02 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-01 13:31:02 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-07-01 13:31:02 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 13:31:02 - StateManager - INFO - Terminated: False
2025-07-01 13:31:02 - StateManager - INFO - Pending transitions (0): []
2025-07-01 13:31:02 - StateManager - INFO - Waiting transitions (0): []
2025-07-01 13:31:02 - StateManager - INFO - Completed transitions (0): []
2025-07-01 13:31:02 - StateManager - INFO - Results stored for 0 transitions
2025-07-01 13:31:02 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-01 13:31:02 - StateManager - INFO - Workflow status: inactive
2025-07-01 13:31:02 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-01 13:31:02 - StateManager - INFO - Workflow status: inactive
2025-07-01 13:31:02 - StateManager - INFO - Workflow paused: False
2025-07-01 13:31:02 - StateManager - INFO - ==============================
2025-07-01 13:31:02 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-07-01 13:31:02 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:31:02 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-07-01 13:31:02 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=initial, execution_type=agent)
2025-07-01 13:31:02 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-07-01 13:31:02 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-07-01 13:31:02 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-07-01 13:31:02 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-07-01 13:31:02 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-07-01 13:31:02 - WorkflowUtils - DEBUG - Filtering out field 'description' with null/empty value: 
2025-07-01 13:31:02 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-07-01 13:31:02 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with empty collection: {}
2025-07-01 13:31:02 - TransitionHandler - DEBUG - tool Parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 13:31:02 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 13:31:02 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:31:02 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-07-01 13:31:02 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: d02d5a74-5546-4346-a184-6d0d1b8598c5) with correlation_id: f08b4c6e-ddd2-4ab0-8898-f69860f935da, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-07-01 13:31:02 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-07-01 13:31:02 - AgentExecutor - DEBUG - Added correlation_id f08b4c6e-ddd2-4ab0-8898-f69860f935da to payload
2025-07-01 13:31:02 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': 'd02d5a74-5546-4346-a184-6d0d1b8598c5', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': 'f08b4c6e-ddd2-4ab0-8898-f69860f935da', 'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'variables': {}, 'agent_config': {'id': '61f8f98e-1263-40b5-af92-2cfaeb3caf9e', 'name': 'AI Agent', 'description': 'Basic AI Agent', 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-07-01 13:31:02 - AgentExecutor - DEBUG - Request d02d5a74-5546-4346-a184-6d0d1b8598c5 sent successfully using provided producer.
2025-07-01 13:31:02 - AgentExecutor - DEBUG - Waiting for single response result for request d02d5a74-5546-4346-a184-6d0d1b8598c5...
2025-07-01 13:31:03 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1137, corr_id: f08b4c6e-ddd2-4ab0-8898-f69860f935da
2025-07-01 13:31:07 - AgentExecutor - DEBUG - Result consumer received message: Offset=24521
2025-07-01 13:31:07 - AgentExecutor - DEBUG - Processing result payload: {'run_id': 'd02d5a74-5546-4346-a184-6d0d1b8598c5', 'session_id': 'd02d5a74-5546-4346-a184-6d0d1b8598c5', 'event_type': None, 'agent_response': {'content': 'Error: Connection error.'}, 'success': False, 'message': 'Message processed successfully', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'component', 'request_id': 'd02d5a74-5546-4346-a184-6d0d1b8598c5'}
2025-07-01 13:31:07 - AgentExecutor - WARNING - Received connection error for request d02d5a74-5546-4346-a184-6d0d1b8598c5, but continuing to wait for potential success response
2025-07-01 13:31:13 - AgentExecutor - DEBUG - Result consumer received message: Offset=24522
2025-07-01 13:31:13 - AgentExecutor - DEBUG - Processing result payload: {'run_id': 'd02d5a74-5546-4346-a184-6d0d1b8598c5', 'session_id': 'd02d5a74-5546-4346-a184-6d0d1b8598c5', 'event_type': None, 'agent_response': {'content': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'source': 'ai_agent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'TextMessage', 'metadata': {}}, 'success': True, 'message': 'Message processed successfully', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'component', 'request_id': 'd02d5a74-5546-4346-a184-6d0d1b8598c5', 'error_code': None, 'details': None}
2025-07-01 13:31:13 - AgentExecutor - DEBUG - Agent response extracted: {'content': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'source': 'ai_agent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'TextMessage', 'metadata': {}}
2025-07-01 13:31:13 - AgentExecutor - DEBUG - Content extracted: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "pa...
2025-07-01 13:31:13 - AgentExecutor - DEBUG - Received valid result for request_id d02d5a74-5546-4346-a184-6d0d1b8598c5
2025-07-01 13:31:13 - AgentExecutor - INFO - Single response received for request d02d5a74-5546-4346-a184-6d0d1b8598c5.
2025-07-01 13:31:13 - TransitionHandler - INFO - Execution result from agent executor: "[ { \"part\": \"(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)\" }, { \"part\": \"Narrator: \"Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?\"\" }, { \"part\": \"(Close-ups of the iPhone\u2019s minimalist and advanced design.)\\n\\nNarrator: \"Check this out \u2013 the latest iPhone redefines what a smartphone can do! Isn't that wild?\"\" }, { \"part\": \"(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: \"Start your day... like, super smoothly. The new chip launches apps faster than ever\u2014whether you\u2019re keeping up with emails or sharing those killer moments!\"\" }, { \"part\": \"(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: \"Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow.\"\" }, { \"part\": \"(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: \"Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?\"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)\" } ]"
2025-07-01 13:31:13 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:31:13 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition Result received.', 'result': {'data': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'data_type': 'string', 'semantic_type': 'string', 'property_name': 'content'}, 'status': 'completed', 'sequence': 2, 'workflow_status': 'running', 'approval_required': False}
2025-07-01 13:31:13 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in memory: {'AgenticAI': {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}, 'status': 'completed', 'timestamp': 1751356873.678902}}
2025-07-01 13:31:14 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-AgenticAI-*************'
2025-07-01 13:31:14 - RedisManager - DEBUG - Set key 'result:transition-AgenticAI-*************' with TTL of 300 seconds
2025-07-01 13:31:14 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 13:31:14 - StateManager - INFO - Marked transition transition-AgenticAI-************* as completed (was_pending=False, was_waiting=False)
2025-07-01 13:31:14 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-AgenticAI-*************'}
2025-07-01 13:31:14 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-AgenticAI-*************
2025-07-01 13:31:14 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-07-01 13:31:14 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-07-01 13:31:14 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-07-01 13:31:14 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-AgenticAI-*************:
2025-07-01 13:31:14 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-01 13:31:14 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-01 13:31:14 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-AgenticAI-*************, returning empty list
2025-07-01 13:31:14 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-AgenticAI-*************
2025-07-01 13:31:14 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 3
2025-07-01 13:31:14 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************', 'transition-CombineTextComponent-*************']
2025-07-01 13:31:14 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-01 13:31:14 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-01 13:31:14 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-AgenticAI-*************
2025-07-01 13:31:14 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-CombineTextComponent-*************
2025-07-01 13:31:14 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-CombineTextComponent-*************
2025-07-01 13:31:14 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']
2025-07-01 13:31:14 - TransitionHandler - INFO - Completed transition transition-AgenticAI-************* in 11.91 seconds
2025-07-01 13:31:14 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:31:14 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'result': 'Completed transition in 11.91 seconds', 'message': 'Transition completed in 11.91 seconds', 'transition_id': 'transition-AgenticAI-*************', 'status': 'time_logged', 'sequence': 3, 'workflow_status': 'running'}
2025-07-01 13:31:14 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-AgenticAI-*************: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']
2025-07-01 13:31:14 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 2
2025-07-01 13:31:14 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']]
2025-07-01 13:31:14 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-*************: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']
2025-07-01 13:31:14 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-01 13:31:14 - EnhancedWorkflowEngine - INFO - Transition transition-AgenticAI-************* completed successfully: 2 next transitions
2025-07-01 13:31:14 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']
2025-07-01 13:31:14 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']
2025-07-01 13:31:14 - EnhancedWorkflowEngine - INFO - Adding transition transition-AgenticAI-************* to pending (all dependencies met)
2025-07-01 13:31:14 - EnhancedWorkflowEngine - INFO - Adding transition transition-CombineTextComponent-************* to waiting (dependencies not yet met)
2025-07-01 13:31:14 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-07-01 13:31:15 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:f08b4c6e-ddd2-4ab0-8898-f69860f935da'
2025-07-01 13:31:15 - RedisManager - DEBUG - Set key 'workflow_state:f08b4c6e-ddd2-4ab0-8898-f69860f935da' with TTL of 600 seconds
2025-07-01 13:31:15 - StateManager - INFO - Workflow state saved to Redis for workflow ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 13:31:15 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-07-01 13:31:15 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-01 13:31:15 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-07-01 13:31:15 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 13:31:15 - StateManager - INFO - Terminated: False
2025-07-01 13:31:15 - StateManager - INFO - Pending transitions (0): []
2025-07-01 13:31:15 - StateManager - INFO - Waiting transitions (1): ['transition-CombineTextComponent-*************']
2025-07-01 13:31:15 - StateManager - INFO - Completed transitions (1): ['transition-AgenticAI-*************']
2025-07-01 13:31:15 - StateManager - INFO - Results stored for 1 transitions
2025-07-01 13:31:15 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 13:31:15 - StateManager - INFO - Workflow status: active
2025-07-01 13:31:15 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 13:31:15 - StateManager - INFO - Workflow status: active
2025-07-01 13:31:15 - StateManager - INFO - Workflow paused: False
2025-07-01 13:31:15 - StateManager - INFO - ==============================
2025-07-01 13:31:15 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-07-01 13:31:15 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:31:15 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 4, 'workflow_status': 'running'}
2025-07-01 13:31:15 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=standard, execution_type=agent)
2025-07-01 13:31:15 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-07-01 13:31:15 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-07-01 13:31:15 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-07-01 13:31:15 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-07-01 13:31:16 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 13:31:16 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 13:31:16 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 13:31:16 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 13:31:16 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 13:31:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:31:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:31:16 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:31:16 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:31:16 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:31:16 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:31:16 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-07-01 13:31:16 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-07-01 13:31:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:31:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:31:16 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:31:16 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:31:16 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:31:16 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:31:16 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → query via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:31:16 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-07-01 13:31:16 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-07-01 13:31:16 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-07-01 13:31:16 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with empty collection: {}
2025-07-01 13:31:16 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_type = component
2025-07-01 13:31:16 - TransitionHandler - DEBUG - 📌 Added static parameter: execution_type = response
2025-07-01 13:31:16 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_config = {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}
2025-07-01 13:31:16 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 13:31:16 - TransitionHandler - DEBUG - tool Parameters: {'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 13:31:16 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 13:31:16 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:31:16 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 5, 'workflow_status': 'running'}
2025-07-01 13:31:16 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 81987a53-dacd-4b2d-9509-895a08af39e4) with correlation_id: f08b4c6e-ddd2-4ab0-8898-f69860f935da, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-07-01 13:31:16 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-07-01 13:31:16 - AgentExecutor - DEBUG - Added correlation_id f08b4c6e-ddd2-4ab0-8898-f69860f935da to payload
2025-07-01 13:31:16 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '81987a53-dacd-4b2d-9509-895a08af39e4', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': 'f08b4c6e-ddd2-4ab0-8898-f69860f935da', 'agent_type': 'component', 'execution_type': 'response', 'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'variables': {}, 'agent_config': {'id': '3582435a-1dd5-4ed7-b58d-254ec96cdc18', 'name': 'AI Agent', 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-07-01 13:31:16 - AgentExecutor - DEBUG - Request 81987a53-dacd-4b2d-9509-895a08af39e4 sent successfully using provided producer.
2025-07-01 13:31:16 - AgentExecutor - DEBUG - Waiting for single response result for request 81987a53-dacd-4b2d-9509-895a08af39e4...
2025-07-01 13:31:19 - AgentExecutor - DEBUG - Result consumer received message: Offset=24523
2025-07-01 13:31:19 - AgentExecutor - DEBUG - Processing result payload: {'run_id': '81987a53-dacd-4b2d-9509-895a08af39e4', 'session_id': '81987a53-dacd-4b2d-9509-895a08af39e4', 'event_type': None, 'agent_response': {'content': '0', 'source': 'ai_agent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'TextMessage', 'metadata': {}}, 'success': True, 'message': 'Message processed successfully', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'component', 'request_id': '81987a53-dacd-4b2d-9509-895a08af39e4', 'error_code': None, 'details': None}
2025-07-01 13:31:19 - AgentExecutor - DEBUG - Agent response extracted: {'content': '0', 'source': 'ai_agent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'TextMessage', 'metadata': {}}
2025-07-01 13:31:19 - AgentExecutor - DEBUG - Content extracted: 0...
2025-07-01 13:31:19 - AgentExecutor - DEBUG - Received valid result for request_id 81987a53-dacd-4b2d-9509-895a08af39e4
2025-07-01 13:31:19 - AgentExecutor - INFO - Single response received for request 81987a53-dacd-4b2d-9509-895a08af39e4.
2025-07-01 13:31:19 - TransitionHandler - INFO - Execution result from agent executor: "0"
2025-07-01 13:31:19 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:31:19 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition Result received.', 'result': {'data': '0', 'data_type': 'string', 'semantic_type': 'string', 'property_name': 'content'}, 'status': 'completed', 'sequence': 6, 'workflow_status': 'running', 'approval_required': False}
2025-07-01 13:31:19 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in memory: {'AgenticAI': {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': '0'}, 'status': 'completed', 'timestamp': 1751356879.727212}}
2025-07-01 13:31:20 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-AgenticAI-*************'
2025-07-01 13:31:20 - RedisManager - DEBUG - Set key 'result:transition-AgenticAI-*************' with TTL of 300 seconds
2025-07-01 13:31:20 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 13:31:20 - StateManager - INFO - Marked transition transition-AgenticAI-************* as completed (was_pending=False, was_waiting=False)
2025-07-01 13:31:20 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'transition-AgenticAI-*************', 'transition-AgenticAI-*************'}
2025-07-01 13:31:20 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-AgenticAI-*************
2025-07-01 13:31:20 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-07-01 13:31:20 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-07-01 13:31:20 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-07-01 13:31:20 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-AgenticAI-*************:
2025-07-01 13:31:20 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-01 13:31:20 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-01 13:31:20 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-AgenticAI-*************, returning empty list
2025-07-01 13:31:20 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-AgenticAI-*************
2025-07-01 13:31:20 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-07-01 13:31:20 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-LoopNode-*************']
2025-07-01 13:31:20 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-01 13:31:20 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-01 13:31:20 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-LoopNode-*************
2025-07-01 13:31:20 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-LoopNode-*************']
2025-07-01 13:31:20 - TransitionHandler - INFO - Completed transition transition-AgenticAI-************* in 4.88 seconds
2025-07-01 13:31:20 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:31:20 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'result': 'Completed transition in 4.88 seconds', 'message': 'Transition completed in 4.88 seconds', 'transition_id': 'transition-AgenticAI-*************', 'status': 'time_logged', 'sequence': 7, 'workflow_status': 'running'}
2025-07-01 13:31:20 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-AgenticAI-*************: ['transition-LoopNode-*************']
2025-07-01 13:31:20 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-07-01 13:31:20 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-LoopNode-*************']]
2025-07-01 13:31:20 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-*************: ['transition-LoopNode-*************']
2025-07-01 13:31:20 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-01 13:31:20 - EnhancedWorkflowEngine - INFO - Transition transition-AgenticAI-************* completed successfully: 1 next transitions
2025-07-01 13:31:20 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-LoopNode-*************']
2025-07-01 13:31:20 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-LoopNode-*************']
2025-07-01 13:31:20 - EnhancedWorkflowEngine - INFO - Adding transition transition-LoopNode-************* to pending (all dependencies met)
2025-07-01 13:31:20 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-07-01 13:31:21 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:f08b4c6e-ddd2-4ab0-8898-f69860f935da'
2025-07-01 13:31:21 - RedisManager - DEBUG - Set key 'workflow_state:f08b4c6e-ddd2-4ab0-8898-f69860f935da' with TTL of 600 seconds
2025-07-01 13:31:21 - StateManager - INFO - Workflow state saved to Redis for workflow ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 13:31:21 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-07-01 13:31:21 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-01 13:31:21 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-07-01 13:31:21 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 13:31:21 - StateManager - INFO - Terminated: False
2025-07-01 13:31:21 - StateManager - INFO - Pending transitions (0): []
2025-07-01 13:31:21 - StateManager - INFO - Waiting transitions (1): ['transition-CombineTextComponent-*************']
2025-07-01 13:31:21 - StateManager - INFO - Completed transitions (2): ['transition-AgenticAI-*************', 'transition-AgenticAI-*************']
2025-07-01 13:31:21 - StateManager - INFO - Results stored for 2 transitions
2025-07-01 13:31:21 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 13:31:21 - StateManager - INFO - Workflow status: active
2025-07-01 13:31:21 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 13:31:21 - StateManager - INFO - Workflow status: active
2025-07-01 13:31:21 - StateManager - INFO - Workflow paused: False
2025-07-01 13:31:21 - StateManager - INFO - ==============================
2025-07-01 13:31:21 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-07-01 13:31:21 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:31:21 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 8, 'workflow_status': 'running'}
2025-07-01 13:31:21 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=standard, execution_type=loop)
2025-07-01 13:31:21 - LoopExecutor - DEBUG - 🔗 Orchestration engine reference set in LoopExecutor
2025-07-01 13:31:21 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-07-01 13:31:21 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-07-01 13:31:21 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-07-01 13:31:21 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-07-01 13:31:21 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-07-01 13:31:22 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 13:31:22 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 13:31:22 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 13:31:22 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 13:31:22 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 13:31:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '0'}
2025-07-01 13:31:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:31:22 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: 0
2025-07-01 13:31:22 - WorkflowUtils - DEBUG - Found result.result: 0 (type: <class 'str'>)
2025-07-01 13:31:22 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:31:22 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:31:22 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-07-01 13:31:22 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-07-01 13:31:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '0'}
2025-07-01 13:31:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:31:22 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: 0
2025-07-01 13:31:22 - WorkflowUtils - DEBUG - Found result.result: 0 (type: <class 'str'>)
2025-07-01 13:31:22 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:31:22 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:31:22 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → start via path 'result': 0
2025-07-01 13:31:22 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-07-01 13:31:22 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-07-01 13:31:22 - WorkflowUtils - DEBUG - Filtering out field 'iteration_list' with empty collection: []
2025-07-01 13:31:22 - WorkflowUtils - DEBUG - Filtering out field 'start' with null/empty value: None
2025-07-01 13:31:22 - WorkflowUtils - INFO - 🧹 Parameter filtering: 14 → 12 fields (2 null/empty fields removed)
2025-07-01 13:31:22 - TransitionHandler - DEBUG - 📌 Added static parameter: source_type = number_range
2025-07-01 13:31:22 - TransitionHandler - DEBUG - 📌 Added static parameter: batch_size = 1
2025-07-01 13:31:22 - TransitionHandler - DEBUG - 📌 Added static parameter: end = 6
2025-07-01 13:31:22 - TransitionHandler - DEBUG - 📌 Added static parameter: step = 1
2025-07-01 13:31:22 - TransitionHandler - DEBUG - 📌 Added static parameter: parallel_execution = False
2025-07-01 13:31:22 - TransitionHandler - DEBUG - 📌 Added static parameter: max_concurrent = 3
2025-07-01 13:31:22 - TransitionHandler - DEBUG - 📌 Added static parameter: preserve_order = True
2025-07-01 13:31:22 - TransitionHandler - DEBUG - 📌 Added static parameter: iteration_timeout = 60
2025-07-01 13:31:22 - TransitionHandler - DEBUG - 📌 Added static parameter: aggregation_type = collect_all
2025-07-01 13:31:22 - TransitionHandler - DEBUG - 📌 Added static parameter: include_metadata = False
2025-07-01 13:31:22 - TransitionHandler - DEBUG - 📌 Added static parameter: on_iteration_error = retry_once
2025-07-01 13:31:22 - TransitionHandler - DEBUG - 📌 Added static parameter: include_errors = True
2025-07-01 13:31:22 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-07-01 13:31:22 - TransitionHandler - DEBUG - tool Parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-07-01 13:31:22 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-07-01 13:31:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:31:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 9, 'workflow_status': 'running'}
2025-07-01 13:31:22 - LoopExecutor - INFO - 🔄 Starting loop execution for transition transition-LoopNode-*************
2025-07-01 13:31:22 - LoopExecutor - INFO - 🔧 Resolving loop configuration for transition transition-LoopNode-*************
2025-07-01 13:31:23 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 13:31:23 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 13:31:23 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 13:31:23 - LoopExecutor - DEBUG - 📋 Resolved loop configuration: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 1, 'end': 6}, 'step': 1, 'source_type': 'number_range'}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}}
2025-07-01 13:31:23 - LoopStateManager - INFO - 🔧 Initializing loop state for loop loop_transition-LoopNode-*************_fa2449c7
2025-07-01 13:31:23 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_fa2449c7, transition_id: transition-LoopNode-*************
2025-07-01 13:31:23 - LoopStateManager - INFO - ✅ Loop state initialized: 6 iterations planned
2025-07-01 13:31:23 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-*************: ['transition-AgenticAI-*************']
2025-07-01 13:31:23 - StateManager - DEBUG - Extracted dependencies for transition transition-LoopNode-*************: ['transition-AgenticAI-*************']
2025-07-01 13:31:23 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-1751005477464: ['transition-LoopNode-*************']
2025-07-01 13:31:23 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 13:31:23 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-*************']
2025-07-01 13:31:23 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-07-01 13:31:23 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-07-01 13:31:23 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 13:31:23 - StateManager - INFO - Built dependency map for 9 transitions
2025-07-01 13:31:23 - StateManager - DEBUG - Transition transition-AgenticAI-************* depends on: ['transition-AgenticAI-*************']
2025-07-01 13:31:23 - StateManager - DEBUG - Transition transition-LoopNode-************* depends on: ['transition-AgenticAI-*************']
2025-07-01 13:31:23 - StateManager - DEBUG - Transition transition-MergeDataComponent-1751005477464 depends on: ['transition-LoopNode-*************']
2025-07-01 13:31:23 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 13:31:23 - StateManager - DEBUG - Transition transition-AgenticAI-1750505490787 depends on: ['transition-CombineTextComponent-*************']
2025-07-01 13:31:23 - StateManager - DEBUG - Transition transition-AgenticAI-1751001474606 depends on: ['transition-AgenticAI-1750505490787']
2025-07-01 13:31:23 - StateManager - DEBUG - Transition transition-AgenticAI-1751001471087 depends on: ['transition-AgenticAI-1750505490787']
2025-07-01 13:31:23 - StateManager - DEBUG - Transition transition-CombineTextComponent-1750920624318 depends on: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 13:31:23 - LoopStateManager - INFO - 🔧 Loop body configured - Entry: ['transition-CombineTextComponent-*************'], Exit: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:31:23 - LoopStateManager - DEBUG - 🔗 Loop body dependencies: {'transition-CombineTextComponent-1750920624318': [], 'transition-CombineTextComponent-*************': []}
2025-07-01 13:31:23 - LoopExecutor - INFO - 🔄 Executing iteration 1/6
2025-07-01 13:31:23 - LoopExecutor - INFO - 🔄 Executing iteration 1 with item: 1
2025-07-01 13:31:23 - LoopStateManager - INFO - 🚀 Starting iteration 1/6
2025-07-01 13:31:23 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_fa2449c7, transition_id: transition-LoopNode-*************
2025-07-01 13:31:23 - LoopStateManager - DEBUG - 📋 Initialized iteration state - Pending: ['transition-CombineTextComponent-*************'], Waiting: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:31:23 - LoopStateManager - DEBUG - 🔄 Cleared iteration state - Ready for new iteration
2025-07-01 13:31:23 - LoopExecutor - DEBUG - 🔄 Executing pending transitions: ['transition-CombineTextComponent-*************']
2025-07-01 13:31:23 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-07-01 13:31:23 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:31:23 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 10, 'workflow_status': 'running'}
2025-07-01 13:31:23 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-07-01 13:31:23 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 13:31:23 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-07-01 13:31:23 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 13:31:23 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 13:31:24 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 13:31:24 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 13:31:24 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 13:31:24 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 13:31:24 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 13:31:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 13:31:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 13:31:24 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 13:31:24 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 13:31:24 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 13:31:24 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 13:31:24 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 13:31:25 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 13:31:27 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation f08b4c6e-ddd2-4ab0-8898-f69860f935da
2025-07-01 13:31:28 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-07-01 13:31:28 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-LoopNode-*************
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:31:28 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (2/3 compatible)
2025-07-01 13:31:28 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (2/3 compatible)
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {}
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 13:31:28 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-07-01 13:31:28 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/3 successful
2025-07-01 13:31:28 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-07-01 13:31:28 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/3 successful
2025-07-01 13:31:28 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 13:31:28 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 13:31:28 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 13:31:28 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-07-01 13:31:28 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 13:31:28 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 13:31:28 - TransitionHandler - DEBUG - tool Parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 13:31:28 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 13:31:28 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 11, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:31:28 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 11, 'workflow_status': 'running'}
2025-07-01 13:31:28 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 99a21e76-be0a-487d-8dc6-ae081074253b) using provided producer.
2025-07-01 13:31:28 - NodeExecutor - DEBUG - Added correlation_id f08b4c6e-ddd2-4ab0-8898-f69860f935da to payload
2025-07-01 13:31:28 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-07-01 13:31:28 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '99a21e76-be0a-487d-8dc6-ae081074253b', 'correlation_id': 'f08b4c6e-ddd2-4ab0-8898-f69860f935da', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-07-01 13:31:28 - NodeExecutor - DEBUG - Request 99a21e76-be0a-487d-8dc6-ae081074253b sent successfully using provided producer.
2025-07-01 13:31:28 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 99a21e76-be0a-487d-8dc6-ae081074253b...
2025-07-01 13:31:28 - AgentExecutor - DEBUG - Result consumer received message: Offset=24524
2025-07-01 13:31:28 - AgentExecutor - DEBUG - Processing result payload: {'run_id': '81987a53-dacd-4b2d-9509-895a08af39e4', 'session_id': '81987a53-dacd-4b2d-9509-895a08af39e4', 'event_type': None, 'agent_response': {'content': 'Error: Connection error.'}, 'success': False, 'message': 'Message processed successfully', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'component', 'request_id': '81987a53-dacd-4b2d-9509-895a08af39e4'}
2025-07-01 13:31:28 - AgentExecutor - WARNING - Received connection error for request 81987a53-dacd-4b2d-9509-895a08af39e4, but continuing to wait for potential success response
2025-07-01 13:32:24 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 13:32:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 13:32:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 13:32:24 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 13:32:42 - NodeExecutor - DEBUG - Result consumer received message: Offset=999
2025-07-01 13:32:42 - NodeExecutor - WARNING - Received error response for request_id 99a21e76-be0a-487d-8dc6-ae081074253b: Error combining text for request_id 99a21e76-be0a-487d-8dc6-ae081074253b: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-07-01 13:32:42 - NodeExecutor - ERROR - Error during node execution 99a21e76-be0a-487d-8dc6-ae081074253b: Node execution failed: Error combining text for request_id 99a21e76-be0a-487d-8dc6-ae081074253b: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 99a21e76-be0a-487d-8dc6-ae081074253b: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-07-01 13:32:42 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-*************': Node execution failed: Error combining text for request_id 99a21e76-be0a-487d-8dc6-ae081074253b: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 383, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 99a21e76-be0a-487d-8dc6-ae081074253b: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

2025-07-01 13:32:42 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 12, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:32:42 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 99a21e76-be0a-487d-8dc6-ae081074253b: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"', 'status': 'failed', 'sequence': 12, 'workflow_status': 'running'}
2025-07-01 13:32:42 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 99a21e76-be0a-487d-8dc6-ae081074253b: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-07-01 13:32:42 - LoopExecutor - ERROR - ❌ Failed to execute transition transition-CombineTextComponent-*************_iteration_0: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 99a21e76-be0a-487d-8dc6-ae081074253b: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-07-01 13:32:42 - LoopExecutor - DEBUG - ✅ Transition transition-CombineTextComponent-************* completed in iteration 0
2025-07-01 13:32:42 - LoopStateManager - DEBUG - ✅ Marked transition-CombineTextComponent-************* as completed
2025-07-01 13:32:42 - LoopStateManager - DEBUG - 🔓 Newly available transitions: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:32:42 - LoopExecutor - DEBUG - 🔄 Executing pending transitions: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:32:42 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-1750920624318
2025-07-01 13:32:42 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 13, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:32:42 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-1750920624318', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-1750920624318', 'status': 'started', 'sequence': 13, 'workflow_status': 'running'}
2025-07-01 13:32:42 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-1750920624318' (type=standard, execution_type=Components)
2025-07-01 13:32:42 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 13:32:42 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-1750920624318
2025-07-01 13:32:42 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 13:32:42 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 13:32:43 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-1750505490787. Trying PostgreSQL next.
2025-07-01 13:32:46 - PostgresManager - DEBUG - No result found for transition transition-AgenticAI-1750505490787 in correlation f08b4c6e-ddd2-4ab0-8898-f69860f935da
2025-07-01 13:32:46 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-AgenticAI-1750505490787. Trying in-memory next.
2025-07-01 13:32:46 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-AgenticAI-1750505490787
2025-07-01 13:32:47 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-1751001471087. Trying PostgreSQL next.
2025-07-01 13:32:49 - PostgresManager - DEBUG - No result found for transition transition-AgenticAI-1751001471087 in correlation f08b4c6e-ddd2-4ab0-8898-f69860f935da
2025-07-01 13:32:49 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-AgenticAI-1751001471087. Trying in-memory next.
2025-07-01 13:32:49 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-AgenticAI-1751001471087
2025-07-01 13:32:50 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-1751001474606. Trying PostgreSQL next.
2025-07-01 13:32:53 - PostgresManager - DEBUG - No result found for transition transition-AgenticAI-1751001474606 in correlation f08b4c6e-ddd2-4ab0-8898-f69860f935da
2025-07-01 13:32:53 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-AgenticAI-1751001474606. Trying in-memory next.
2025-07-01 13:32:53 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-AgenticAI-1751001474606
2025-07-01 13:32:53 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-07-01 13:32:53 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 13:32:53 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-07-01 13:32:53 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 13:32:53 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 13:32:53 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 13:32:53 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 13:32:53 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 13:32:53 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 13:32:53 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 13:32:53 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 13:32:53 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 13:32:53 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 13:32:53 - TransitionHandler - DEBUG - tool Parameters: {'num_additional_inputs': '2', 'separator': '\\n'}
2025-07-01 13:32:53 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-1750920624318' with parameters: {'num_additional_inputs': '2', 'separator': '\\n'}
2025-07-01 13:32:53 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 14, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:32:53 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'transition_id': 'transition-CombineTextComponent-1750920624318', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 14, 'workflow_status': 'running'}
2025-07-01 13:32:53 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: a2e315c8-ebf1-4195-820d-a784c6f027e7) using provided producer.
2025-07-01 13:32:53 - NodeExecutor - DEBUG - Added correlation_id f08b4c6e-ddd2-4ab0-8898-f69860f935da to payload
2025-07-01 13:32:53 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-1750920624318 to payload
2025-07-01 13:32:53 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'num_additional_inputs': '2', 'separator': '\\n'}, 'request_id': 'a2e315c8-ebf1-4195-820d-a784c6f027e7', 'correlation_id': 'f08b4c6e-ddd2-4ab0-8898-f69860f935da', 'transition_id': 'transition-CombineTextComponent-1750920624318'}
2025-07-01 13:32:53 - NodeExecutor - DEBUG - Request a2e315c8-ebf1-4195-820d-a784c6f027e7 sent successfully using provided producer.
2025-07-01 13:32:53 - NodeExecutor - DEBUG - Waiting indefinitely for result for request a2e315c8-ebf1-4195-820d-a784c6f027e7...
2025-07-01 13:32:54 - NodeExecutor - DEBUG - Result consumer received message: Offset=1000
2025-07-01 13:32:54 - NodeExecutor - WARNING - Received error response for request_id a2e315c8-ebf1-4195-820d-a784c6f027e7: Error combining text for request_id a2e315c8-ebf1-4195-820d-a784c6f027e7: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-07-01 13:32:54 - NodeExecutor - ERROR - Error during node execution a2e315c8-ebf1-4195-820d-a784c6f027e7: Node execution failed: Error combining text for request_id a2e315c8-ebf1-4195-820d-a784c6f027e7: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id a2e315c8-ebf1-4195-820d-a784c6f027e7: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-07-01 13:32:54 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-1750920624318': Node execution failed: Error combining text for request_id a2e315c8-ebf1-4195-820d-a784c6f027e7: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 383, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id a2e315c8-ebf1-4195-820d-a784c6f027e7: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

2025-07-01 13:32:54 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 15, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:32:54 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'transition_id': 'transition-CombineTextComponent-1750920624318', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id a2e315c8-ebf1-4195-820d-a784c6f027e7: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"', 'status': 'failed', 'sequence': 15, 'workflow_status': 'running'}
2025-07-01 13:32:54 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-1750920624318: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id a2e315c8-ebf1-4195-820d-a784c6f027e7: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-07-01 13:32:54 - LoopExecutor - ERROR - ❌ Failed to execute transition transition-CombineTextComponent-1750920624318_iteration_0: Exception in transition transition-CombineTextComponent-1750920624318: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id a2e315c8-ebf1-4195-820d-a784c6f027e7: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-07-01 13:32:54 - LoopExecutor - DEBUG - ✅ Transition transition-CombineTextComponent-1750920624318 completed in iteration 0
2025-07-01 13:32:54 - LoopStateManager - DEBUG - ✅ Marked transition-CombineTextComponent-1750920624318 as completed
2025-07-01 13:32:54 - LoopExecutor - INFO - 🏁 Loop body execution completed for iteration 0
2025-07-01 13:32:54 - LoopStateManager - INFO - ✅ Completing iteration 1/6 with status: completed
2025-07-01 13:32:54 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_fa2449c7, transition_id: transition-LoopNode-*************
2025-07-01 13:32:54 - LoopExecutor - INFO - ✅ Iteration 1 completed successfully
2025-07-01 13:32:54 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_fa2449c7, transition_id: transition-LoopNode-*************
2025-07-01 13:32:54 - LoopExecutor - INFO - 🔄 Executing iteration 2/6
2025-07-01 13:32:54 - LoopExecutor - INFO - 🔄 Executing iteration 2 with item: 2
2025-07-01 13:32:54 - LoopStateManager - INFO - 🚀 Starting iteration 2/6
2025-07-01 13:32:54 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_fa2449c7, transition_id: transition-LoopNode-*************
2025-07-01 13:32:54 - LoopStateManager - DEBUG - 📋 Initialized iteration state - Pending: ['transition-CombineTextComponent-*************'], Waiting: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:32:54 - LoopStateManager - DEBUG - 🔄 Cleared iteration state - Ready for new iteration
2025-07-01 13:32:54 - LoopExecutor - DEBUG - 🔄 Executing pending transitions: ['transition-CombineTextComponent-*************']
2025-07-01 13:32:54 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-07-01 13:32:54 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 16, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:32:54 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 16, 'workflow_status': 'running'}
2025-07-01 13:32:54 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-07-01 13:32:54 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 13:32:54 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-07-01 13:32:54 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 13:32:54 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 13:32:54 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 13:32:54 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 13:32:54 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 13:32:54 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 13:32:55 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 13:32:55 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 13:32:55 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 13:32:55 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 13:32:56 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 13:32:58 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation f08b4c6e-ddd2-4ab0-8898-f69860f935da
2025-07-01 13:32:58 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-07-01 13:32:58 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-LoopNode-*************
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:32:58 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (2/3 compatible)
2025-07-01 13:32:58 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (2/3 compatible)
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {}
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 13:32:58 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-07-01 13:32:58 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/3 successful
2025-07-01 13:32:58 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-07-01 13:32:58 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/3 successful
2025-07-01 13:32:58 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 13:32:58 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 13:32:58 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 13:32:58 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-07-01 13:32:58 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 13:32:58 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 13:32:58 - TransitionHandler - DEBUG - tool Parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 13:32:58 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 13:32:58 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 17, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:32:58 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 17, 'workflow_status': 'running'}
2025-07-01 13:32:58 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 6c9431cf-4c45-438a-ba14-a23522a44b95) using provided producer.
2025-07-01 13:32:58 - NodeExecutor - DEBUG - Added correlation_id f08b4c6e-ddd2-4ab0-8898-f69860f935da to payload
2025-07-01 13:32:58 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-07-01 13:32:58 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '6c9431cf-4c45-438a-ba14-a23522a44b95', 'correlation_id': 'f08b4c6e-ddd2-4ab0-8898-f69860f935da', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-07-01 13:32:58 - NodeExecutor - DEBUG - Request 6c9431cf-4c45-438a-ba14-a23522a44b95 sent successfully using provided producer.
2025-07-01 13:32:58 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 6c9431cf-4c45-438a-ba14-a23522a44b95...
2025-07-01 13:33:03 - NodeExecutor - DEBUG - Result consumer received message: Offset=1001
2025-07-01 13:33:03 - NodeExecutor - WARNING - Received error response for request_id 6c9431cf-4c45-438a-ba14-a23522a44b95: Error combining text for request_id 6c9431cf-4c45-438a-ba14-a23522a44b95: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-07-01 13:33:03 - NodeExecutor - ERROR - Error during node execution 6c9431cf-4c45-438a-ba14-a23522a44b95: Node execution failed: Error combining text for request_id 6c9431cf-4c45-438a-ba14-a23522a44b95: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 6c9431cf-4c45-438a-ba14-a23522a44b95: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-07-01 13:33:03 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-*************': Node execution failed: Error combining text for request_id 6c9431cf-4c45-438a-ba14-a23522a44b95: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 383, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 6c9431cf-4c45-438a-ba14-a23522a44b95: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

2025-07-01 13:33:03 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 18, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:33:03 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 6c9431cf-4c45-438a-ba14-a23522a44b95: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"', 'status': 'failed', 'sequence': 18, 'workflow_status': 'running'}
2025-07-01 13:33:03 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 6c9431cf-4c45-438a-ba14-a23522a44b95: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-07-01 13:33:03 - LoopExecutor - ERROR - ❌ Failed to execute transition transition-CombineTextComponent-*************_iteration_1: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 6c9431cf-4c45-438a-ba14-a23522a44b95: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-07-01 13:33:03 - LoopExecutor - DEBUG - ✅ Transition transition-CombineTextComponent-************* completed in iteration 1
2025-07-01 13:33:03 - LoopStateManager - DEBUG - ✅ Marked transition-CombineTextComponent-************* as completed
2025-07-01 13:33:03 - LoopStateManager - DEBUG - 🔓 Newly available transitions: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:33:03 - LoopExecutor - DEBUG - 🔄 Executing pending transitions: ['transition-CombineTextComponent-1750920624318']
2025-07-01 13:33:03 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-1750920624318
2025-07-01 13:33:03 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 19, corr_id f08b4c6e-ddd2-4ab0-8898-f69860f935da):
2025-07-01 13:33:03 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-1750920624318', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-1750920624318', 'status': 'started', 'sequence': 19, 'workflow_status': 'running'}
2025-07-01 13:33:03 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-1750920624318' (type=standard, execution_type=Components)
2025-07-01 13:33:03 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 13:33:03 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-1750920624318
2025-07-01 13:33:03 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 13:33:03 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 13:33:05 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-1750505490787. Trying PostgreSQL next.
2025-07-01 13:33:06 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-07-01 13:33:06 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-07-01 13:33:06 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-07-01 13:33:06 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-07-01 13:33:06 - EnhancedWorkflowEngine - WARNING - Workflow f08b4c6e-ddd2-4ab0-8898-f69860f935da execution was cancelled!
2025-07-01 13:33:06 - StateManager - INFO - Workflow terminated flag set to: True
2025-07-01 13:33:06 - KafkaWorkflowConsumer - WARNING - Workflow execution for '0c19c070-905e-46ef-9f57-62eb427bf396' was cancelled
2025-07-01 13:33:06 - KafkaWorkflowConsumer - INFO - Workflow '0c19c070-905e-46ef-9f57-62eb427bf396' final status: cancelled, result: Workflow '0c19c070-905e-46ef-9f57-62eb427bf396' execution was cancelled
2025-07-01 13:33:06 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f08b4c6e-ddd2-4ab0-8898-f69860f935da, response: {'status': 'Workflow Cancelled', 'result': "Workflow '0c19c070-905e-46ef-9f57-62eb427bf396' execution was cancelled", 'workflow_status': 'cancelled'}
2025-07-01 13:33:06 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: f08b4c6e-ddd2-4ab0-8898-f69860f935da 
2025-07-01 13:33:06 - Main - ERROR - Shutting down due to keyboard interrupt...
