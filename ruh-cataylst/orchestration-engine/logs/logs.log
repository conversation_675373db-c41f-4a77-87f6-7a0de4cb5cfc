2025-07-01 12:46:11 - Main - INFO - Starting Server
2025-07-01 12:46:11 - Main - INFO - Connection at: **************:9092
2025-07-01 12:46:11 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-01 12:46:11 - NodeExecutor - INFO - NodeExecutor initialized.
2025-07-01 12:46:11 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-01 12:46:11 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-07-01 12:46:11 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 12:46:13 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 12:46:13 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 12:46:15 - <PERSON><PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 12:46:17 - PostgresManager - INFO - PostgreSQL connection pool created
2025-07-01 12:46:17 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-07-01 12:46:20 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-07-01 12:46:21 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-07-01 12:46:21 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 12:46:22 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 12:46:22 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 12:46:24 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 12:46:24 - RedisEventListener - INFO - Starting Redis event listener thread
2025-07-01 12:46:24 - RedisEventListener - INFO - Redis event listener started
2025-07-01 12:46:24 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-07-01 12:46:24 - StateManager - DEBUG - Using provided database connections
2025-07-01 12:46:24 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 12:46:24 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 12:46:24 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 12:46:25 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-07-01 12:46:25 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 12:46:25 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 12:46:25 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-07-01 12:46:25 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-07-01 12:46:26 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-07-01 12:46:26 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-07-01 12:46:28 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-07-01 12:46:28 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-07-01 12:46:28 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-07-01 12:46:40 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-07-01 12:46:46 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-07-01 12:46:46 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-07-01 12:46:46 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-07-01 12:46:53 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-07-01 12:46:53 - NodeExecutor - INFO - Background result consumer loop started.
2025-07-01 12:46:53 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-07-01 12:46:54 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-AgenticAI-*************', 'data': b'expired'}
2025-07-01 12:46:54 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-AgenticAI-*************'
2025-07-01 12:46:54 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-AgenticAI-*************
2025-07-01 12:46:54 - RedisEventListener - DEBUG - Extracted key: result:transition-AgenticAI-*************
2025-07-01 12:46:54 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 12:46:54 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 12:46:54 - RedisEventListener - INFO - Detected expired event for result of transition: transition-AgenticAI-*************
2025-07-01 12:46:54 - RedisEventListener - INFO - Archiving result for transition: transition-AgenticAI-*************
2025-07-01 12:46:54 - StateManager - DEBUG - Attempting to archive result for transition transition-AgenticAI-*************
2025-07-01 12:46:54 - StateManager - DEBUG - Provided result: False
2025-07-01 12:46:55 - StateManager - DEBUG - Trying to get result from Redis for transition transition-AgenticAI-*************
2025-07-01 12:46:55 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-*************
2025-07-01 12:46:55 - StateManager - DEBUG - Trying to get result from memory for transition transition-AgenticAI-*************
2025-07-01 12:46:55 - StateManager - DEBUG - No result found in memory for transition transition-AgenticAI-*************
2025-07-01 12:46:55 - StateManager - DEBUG - Available transition results in memory: []
2025-07-01 12:46:55 - StateManager - WARNING - No result found to archive for transition transition-AgenticAI-*************
2025-07-01 12:46:59 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-07-01 12:46:59 - AgentExecutor - INFO - Background result consumer loop started.
2025-07-01 12:46:59 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1135
2025-07-01 12:46:59 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751354176, 'task_type': 'workflow', 'data': {'workflow_id': '0c19c070-905e-46ef-9f57-62eb427bf396', 'payload': {'user_dependent_fields': ['query'], 'user_payload_template': {'query': {'value': 'nature 101', 'transition_id': 'AgenticAI-*************'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-07-01 12:46:59 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: 0c19c070-905e-46ef-9f57-62eb427bf396
2025-07-01 12:46:59 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/0c19c070-905e-46ef-9f57-62eb427bf396
2025-07-01 12:46:59 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-AgenticAI-*************', 'data': b'expired'}
2025-07-01 12:46:59 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-AgenticAI-*************'
2025-07-01 12:46:59 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-AgenticAI-*************
2025-07-01 12:46:59 - RedisEventListener - DEBUG - Extracted key: result:transition-AgenticAI-*************
2025-07-01 12:46:59 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 12:46:59 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 12:46:59 - RedisEventListener - INFO - Detected expired event for result of transition: transition-AgenticAI-*************
2025-07-01 12:46:59 - RedisEventListener - INFO - Archiving result for transition: transition-AgenticAI-*************
2025-07-01 12:46:59 - StateManager - DEBUG - Attempting to archive result for transition transition-AgenticAI-*************
2025-07-01 12:47:00 - WorkflowService - DEBUG - Received response with status code: 200
2025-07-01 12:47:00 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Ruh_Video_Generation retrieved successfully",
  "workflow": {
    "id": "0c19c070-905e-46ef-9f57-62eb427bf396",
    "name": "Ruh_Video_Generation",
    "description": "Ruh_Video_Generation",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/e2c5a041-2b84-4b33-be90-3d1e07e44353.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/451d0c28-d066-4833-afad-080c37e7563e.json",
    "start_nodes": [
      {
        "field": "query",
        "type": "string",
        "transition_id": "transition-AgenticAI-*************"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": "8d4b3f88-71ea-48dd-b8a2-dc101d22bf8a",
    "template_owner_id": "180c7469-1db0-4707-bc17-eeaa5e7ff64d",
    "is_imported": true,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-29T08:29:44.634200",
    "updated_at": "2025-06-30T08:40:55.500303",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-1751005477464"
      },
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-1750920624318"
      }
    ],
    "is_updated": true
  }
}
2025-07-01 12:47:00 - StateManager - DEBUG - Provided result: False
2025-07-01 12:47:01 - StateManager - DEBUG - Trying to get result from Redis for transition transition-AgenticAI-*************
2025-07-01 12:47:01 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-*************
2025-07-01 12:47:01 - StateManager - DEBUG - Trying to get result from memory for transition transition-AgenticAI-*************
2025-07-01 12:47:01 - StateManager - DEBUG - No result found in memory for transition transition-AgenticAI-*************
2025-07-01 12:47:01 - StateManager - DEBUG - Available transition results in memory: []
2025-07-01 12:47:01 - StateManager - WARNING - No result found to archive for transition transition-AgenticAI-*************
2025-07-01 12:47:02 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:1bc9ffc9-f894-47fe-b016-3d00a7df8369', 'data': b'expired'}
2025-07-01 12:47:02 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:1bc9ffc9-f894-47fe-b016-3d00a7df8369'
2025-07-01 12:47:02 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:1bc9ffc9-f894-47fe-b016-3d00a7df8369
2025-07-01 12:47:02 - RedisEventListener - DEBUG - Extracted key: workflow_state:1bc9ffc9-f894-47fe-b016-3d00a7df8369
2025-07-01 12:47:02 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 12:47:02 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 12:47:02 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: 1bc9ffc9-f894-47fe-b016-3d00a7df8369
2025-07-01 12:47:02 - RedisEventListener - INFO - Archiving workflow state for workflow: 1bc9ffc9-f894-47fe-b016-3d00a7df8369
2025-07-01 12:47:06 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-07-01 12:47:07 - PostgresManager - DEBUG - Updated workflow state for correlation_id: temp_initialization
2025-07-01 12:47:07 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: temp_initialization
2025-07-01 12:47:21 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 12:47:21 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 12:47:21 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 12:47:21 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 12:47:30 - WorkflowService - ERROR - Unable to retrieve the workflow from URL: HTTPSConnectionPool(host='storage.googleapis.com', port=443): Max retries exceeded with url: /ruh-dev/workflows/e2c5a041-2b84-4b33-be90-3d1e07e44353.json (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x1057e8f50>: Failed to resolve 'storage.googleapis.com' ([Errno 8] nodename nor servname provided, or not known)"))
2025-07-01 12:47:30 - KafkaWorkflowConsumer - ERROR - Failed to fetch workflow: Unable to retrieve the workflow from URL
2025-07-01 12:47:30 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e134ae4a-4839-46b0-9bfd-e2cac840714f, response: {'status': 'error', 'workflow_status': 'failed', 'result': 'Failed to fetch workflow: Unable to retrieve the workflow from URL', 'message': 'Failed to fetch workflow: Unable to retrieve the workflow from URL', 'sequence': 0}
2025-07-01 12:48:06 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1136
2025-07-01 12:48:06 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751354285, 'task_type': 'workflow', 'data': {'workflow_id': '0c19c070-905e-46ef-9f57-62eb427bf396', 'payload': {'user_dependent_fields': ['query'], 'user_payload_template': {'query': {'value': 'nature 101', 'transition_id': 'AgenticAI-*************'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-07-01 12:48:06 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: 0c19c070-905e-46ef-9f57-62eb427bf396
2025-07-01 12:48:06 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/0c19c070-905e-46ef-9f57-62eb427bf396
2025-07-01 12:48:07 - WorkflowService - DEBUG - Received response with status code: 200
2025-07-01 12:48:07 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Ruh_Video_Generation retrieved successfully",
  "workflow": {
    "id": "0c19c070-905e-46ef-9f57-62eb427bf396",
    "name": "Ruh_Video_Generation",
    "description": "Ruh_Video_Generation",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/e2c5a041-2b84-4b33-be90-3d1e07e44353.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/451d0c28-d066-4833-afad-080c37e7563e.json",
    "start_nodes": [
      {
        "field": "query",
        "type": "string",
        "transition_id": "transition-AgenticAI-*************"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": "8d4b3f88-71ea-48dd-b8a2-dc101d22bf8a",
    "template_owner_id": "180c7469-1db0-4707-bc17-eeaa5e7ff64d",
    "is_imported": true,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-29T08:29:44.634200",
    "updated_at": "2025-06-30T08:40:55.500303",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-1751005477464"
      },
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-1750920624318"
      }
    ],
    "is_updated": true
  }
}
2025-07-01 12:48:08 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 0c19c070-905e-46ef-9f57-62eb427bf396 - server_script_path is optional
2025-07-01 12:48:08 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-07-01 12:48:08 - StateManager - DEBUG - Using global database connections from initializer
2025-07-01 12:48:08 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 12:48:08 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 12:48:08 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 12:48:09 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 12:48:09 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 12:48:09 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-07-01 12:48:09 - StateManager - DEBUG - Using provided database connections
2025-07-01 12:48:09 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 12:48:09 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 12:48:09 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 12:48:10 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 12:48:10 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 12:48:10 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-*************: ['transition-AgenticAI-*************']
2025-07-01 12:48:10 - StateManager - DEBUG - Extracted dependencies for transition transition-LoopNode-*************: ['transition-AgenticAI-*************']
2025-07-01 12:48:10 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-1751005477464: ['transition-LoopNode-*************']
2025-07-01 12:48:10 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 12:48:10 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-*************']
2025-07-01 12:48:10 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-07-01 12:48:10 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-07-01 12:48:10 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 12:48:10 - StateManager - INFO - Built dependency map for 9 transitions
2025-07-01 12:48:10 - StateManager - DEBUG - Transition transition-AgenticAI-************* depends on: ['transition-AgenticAI-*************']
2025-07-01 12:48:10 - StateManager - DEBUG - Transition transition-LoopNode-************* depends on: ['transition-AgenticAI-*************']
2025-07-01 12:48:10 - StateManager - DEBUG - Transition transition-MergeDataComponent-1751005477464 depends on: ['transition-LoopNode-*************']
2025-07-01 12:48:10 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 12:48:10 - StateManager - DEBUG - Transition transition-AgenticAI-1750505490787 depends on: ['transition-CombineTextComponent-*************']
2025-07-01 12:48:10 - StateManager - DEBUG - Transition transition-AgenticAI-1751001474606 depends on: ['transition-AgenticAI-1750505490787']
2025-07-01 12:48:10 - StateManager - DEBUG - Transition transition-AgenticAI-1751001471087 depends on: ['transition-AgenticAI-1750505490787']
2025-07-01 12:48:10 - StateManager - DEBUG - Transition transition-CombineTextComponent-1750920624318 depends on: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 12:48:10 - MCPToolExecutor - DEBUG - Set correlation ID to: 467b239f-abec-452e-942b-93b5994c7bfb
2025-07-01 12:48:10 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 467b239f-abec-452e-942b-93b5994c7bfb in tool_executor
2025-07-01 12:48:10 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-07-01 12:48:10 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-07-01 12:48:10 - NodeExecutor - DEBUG - Set correlation ID to: 467b239f-abec-452e-942b-93b5994c7bfb
2025-07-01 12:48:10 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 467b239f-abec-452e-942b-93b5994c7bfb in node_executor
2025-07-01 12:48:10 - AgentExecutor - DEBUG - Set correlation ID to: 467b239f-abec-452e-942b-93b5994c7bfb
2025-07-01 12:48:10 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 467b239f-abec-452e-942b-93b5994c7bfb in agent_executor
2025-07-01 12:48:10 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-07-01 12:48:10 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-07-01 12:48:10 - TransitionHandler - INFO - TransitionHandler initialized
2025-07-01 12:48:10 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 467b239f-abec-452e-942b-93b5994c7bfb
2025-07-01 12:48:10 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 467b239f-abec-452e-942b-93b5994c7bfb
2025-07-01 12:48:10 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 467b239f-abec-452e-942b-93b5994c7bfb, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-07-01 12:48:10 - StateManager - INFO - Workflow initialized with initial transition: transition-AgenticAI-*************
2025-07-01 12:48:10 - StateManager - DEBUG - State: pending={'transition-AgenticAI-*************'}, waiting=set(), completed=set()
2025-07-01 12:48:10 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-AgenticAI-*************
2025-07-01 12:48:10 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-07-01 12:48:11 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:467b239f-abec-452e-942b-93b5994c7bfb'
2025-07-01 12:48:11 - RedisManager - DEBUG - Set key 'workflow_state:467b239f-abec-452e-942b-93b5994c7bfb' with TTL of 600 seconds
2025-07-01 12:48:11 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 467b239f-abec-452e-942b-93b5994c7bfb. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 12:48:11 - StateManager - DEBUG - Checking waiting transitions: set()
2025-07-01 12:48:11 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-01 12:48:11 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-07-01 12:48:11 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 12:48:11 - StateManager - INFO - Terminated: False
2025-07-01 12:48:11 - StateManager - INFO - Pending transitions (0): []
2025-07-01 12:48:11 - StateManager - INFO - Waiting transitions (0): []
2025-07-01 12:48:11 - StateManager - INFO - Completed transitions (0): []
2025-07-01 12:48:11 - StateManager - INFO - Results stored for 0 transitions
2025-07-01 12:48:11 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-01 12:48:11 - StateManager - INFO - Workflow status: inactive
2025-07-01 12:48:11 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-01 12:48:11 - StateManager - INFO - Workflow status: inactive
2025-07-01 12:48:11 - StateManager - INFO - Workflow paused: False
2025-07-01 12:48:11 - StateManager - INFO - ==============================
2025-07-01 12:48:11 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-07-01 12:48:11 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 467b239f-abec-452e-942b-93b5994c7bfb):
2025-07-01 12:48:11 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 467b239f-abec-452e-942b-93b5994c7bfb, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-07-01 12:48:11 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=initial, execution_type=agent)
2025-07-01 12:48:11 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-07-01 12:48:11 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-07-01 12:48:11 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-07-01 12:48:11 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-07-01 12:48:11 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-07-01 12:48:11 - WorkflowUtils - DEBUG - Filtering out field 'description' with null/empty value: 
2025-07-01 12:48:11 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-07-01 12:48:11 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with empty collection: {}
2025-07-01 12:48:11 - TransitionHandler - DEBUG - tool Parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 12:48:11 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 12:48:11 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 467b239f-abec-452e-942b-93b5994c7bfb):
2025-07-01 12:48:11 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 467b239f-abec-452e-942b-93b5994c7bfb, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-07-01 12:48:11 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 2147b671-2934-408c-8fb7-5e430805c683) with correlation_id: 467b239f-abec-452e-942b-93b5994c7bfb, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-07-01 12:48:11 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-07-01 12:48:11 - AgentExecutor - DEBUG - Added correlation_id 467b239f-abec-452e-942b-93b5994c7bfb to payload
2025-07-01 12:48:11 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '2147b671-2934-408c-8fb7-5e430805c683', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': '467b239f-abec-452e-942b-93b5994c7bfb', 'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'variables': {}, 'agent_config': {'id': '51475870-d306-45a9-ab27-44618afa799c', 'name': 'AI Agent', 'description': 'Basic AI Agent', 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-07-01 12:48:11 - AgentExecutor - DEBUG - Request 2147b671-2934-408c-8fb7-5e430805c683 sent successfully using provided producer.
2025-07-01 12:48:11 - AgentExecutor - DEBUG - Waiting for single response result for request 2147b671-2934-408c-8fb7-5e430805c683...
2025-07-01 12:48:11 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1136, corr_id: 467b239f-abec-452e-942b-93b5994c7bfb
2025-07-01 12:48:15 - AgentExecutor - DEBUG - Result consumer received message: Offset=24517
2025-07-01 12:48:15 - AgentExecutor - DEBUG - Processing result payload: {'run_id': '2147b671-2934-408c-8fb7-5e430805c683', 'session_id': '2147b671-2934-408c-8fb7-5e430805c683', 'event_type': None, 'agent_response': {'content': 'Error: Connection error.'}, 'success': False, 'message': 'Message processed successfully', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'component', 'request_id': '2147b671-2934-408c-8fb7-5e430805c683'}
2025-07-01 12:48:15 - AgentExecutor - WARNING - Received connection error for request 2147b671-2934-408c-8fb7-5e430805c683, but continuing to wait for potential success response
2025-07-01 12:48:20 - AgentExecutor - DEBUG - Result consumer received message: Offset=24518
2025-07-01 12:48:20 - AgentExecutor - DEBUG - Processing result payload: {'run_id': '2147b671-2934-408c-8fb7-5e430805c683', 'session_id': '2147b671-2934-408c-8fb7-5e430805c683', 'event_type': None, 'agent_response': {'content': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] ', 'source': 'ai_agent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'TextMessage', 'metadata': {}}, 'success': True, 'message': 'Message processed successfully', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'component', 'request_id': '2147b671-2934-408c-8fb7-5e430805c683', 'error_code': None, 'details': None}
2025-07-01 12:48:20 - AgentExecutor - DEBUG - Agent response extracted: {'content': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] ', 'source': 'ai_agent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'TextMessage', 'metadata': {}}
2025-07-01 12:48:20 - AgentExecutor - DEBUG - Content extracted: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "pa...
2025-07-01 12:48:20 - AgentExecutor - DEBUG - Received valid result for request_id 2147b671-2934-408c-8fb7-5e430805c683
2025-07-01 12:48:20 - AgentExecutor - INFO - Single response received for request 2147b671-2934-408c-8fb7-5e430805c683.
2025-07-01 12:48:20 - TransitionHandler - INFO - Execution result from agent executor: "[ { \"part\": \"(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)\" }, { \"part\": \"Narrator: \"Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?\"\" }, { \"part\": \"(Close-ups of the iPhone\u2019s minimalist and advanced design.)\\n\\nNarrator: \"Check this out \u2013 the latest iPhone redefines what a smartphone can do! Isn't that wild?\"\" }, { \"part\": \"(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: \"Start your day... like, super smoothly. The new chip launches apps faster than ever\u2014whether you\u2019re keeping up with emails or sharing those killer moments!\"\" }, { \"part\": \"(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: \"Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow.\"\" }, { \"part\": \"(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: \"Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?\"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)\" } ] "
2025-07-01 12:48:20 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 467b239f-abec-452e-942b-93b5994c7bfb):
2025-07-01 12:48:20 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 467b239f-abec-452e-942b-93b5994c7bfb, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition Result received.', 'result': {'data': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] ', 'data_type': 'string', 'semantic_type': 'string', 'property_name': 'content'}, 'status': 'completed', 'sequence': 2, 'workflow_status': 'running', 'approval_required': False}
2025-07-01 12:48:20 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in memory: {'AgenticAI': {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] '}, 'status': 'completed', 'timestamp': 1751354300.851959}}
2025-07-01 12:48:21 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 12:48:21 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 12:48:21 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 12:48:21 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 12:48:21 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-AgenticAI-*************'
2025-07-01 12:48:21 - RedisManager - DEBUG - Set key 'result:transition-AgenticAI-*************' with TTL of 300 seconds
2025-07-01 12:48:21 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 12:48:21 - StateManager - INFO - Marked transition transition-AgenticAI-************* as completed (was_pending=False, was_waiting=False)
2025-07-01 12:48:21 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-AgenticAI-*************'}
2025-07-01 12:48:21 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-AgenticAI-*************
2025-07-01 12:48:21 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-07-01 12:48:21 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-07-01 12:48:21 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-07-01 12:48:21 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-AgenticAI-*************:
2025-07-01 12:48:21 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-01 12:48:21 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-01 12:48:21 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-AgenticAI-*************, returning empty list
2025-07-01 12:48:21 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-AgenticAI-*************
2025-07-01 12:48:21 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 3
2025-07-01 12:48:21 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************', 'transition-CombineTextComponent-*************']
2025-07-01 12:48:21 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-01 12:48:21 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-01 12:48:21 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-AgenticAI-*************
2025-07-01 12:48:21 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-CombineTextComponent-*************
2025-07-01 12:48:21 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-CombineTextComponent-*************
2025-07-01 12:48:21 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-07-01 12:48:21 - TransitionHandler - INFO - Completed transition transition-AgenticAI-************* in 10.46 seconds
2025-07-01 12:48:21 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id 467b239f-abec-452e-942b-93b5994c7bfb):
2025-07-01 12:48:21 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 467b239f-abec-452e-942b-93b5994c7bfb, response: {'result': 'Completed transition in 10.46 seconds', 'message': 'Transition completed in 10.46 seconds', 'transition_id': 'transition-AgenticAI-*************', 'status': 'time_logged', 'sequence': 3, 'workflow_status': 'running'}
2025-07-01 12:48:21 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-AgenticAI-*************: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-07-01 12:48:21 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 2
2025-07-01 12:48:21 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']]
2025-07-01 12:48:21 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-*************: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-07-01 12:48:21 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-01 12:48:21 - EnhancedWorkflowEngine - INFO - Transition transition-AgenticAI-************* completed successfully: 2 next transitions
2025-07-01 12:48:21 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-07-01 12:48:21 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-07-01 12:48:21 - EnhancedWorkflowEngine - INFO - Adding transition transition-CombineTextComponent-************* to waiting (dependencies not yet met)
2025-07-01 12:48:21 - EnhancedWorkflowEngine - INFO - Adding transition transition-AgenticAI-************* to pending (all dependencies met)
2025-07-01 12:48:21 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-07-01 12:48:22 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:467b239f-abec-452e-942b-93b5994c7bfb'
2025-07-01 12:48:22 - RedisManager - DEBUG - Set key 'workflow_state:467b239f-abec-452e-942b-93b5994c7bfb' with TTL of 600 seconds
2025-07-01 12:48:22 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 467b239f-abec-452e-942b-93b5994c7bfb. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 12:48:22 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-07-01 12:48:22 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-01 12:48:22 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-07-01 12:48:22 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 12:48:22 - StateManager - INFO - Terminated: False
2025-07-01 12:48:22 - StateManager - INFO - Pending transitions (0): []
2025-07-01 12:48:22 - StateManager - INFO - Waiting transitions (1): ['transition-CombineTextComponent-*************']
2025-07-01 12:48:22 - StateManager - INFO - Completed transitions (1): ['transition-AgenticAI-*************']
2025-07-01 12:48:22 - StateManager - INFO - Results stored for 1 transitions
2025-07-01 12:48:22 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 12:48:22 - StateManager - INFO - Workflow status: active
2025-07-01 12:48:22 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 12:48:22 - StateManager - INFO - Workflow status: active
2025-07-01 12:48:22 - StateManager - INFO - Workflow paused: False
2025-07-01 12:48:22 - StateManager - INFO - ==============================
2025-07-01 12:48:22 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-07-01 12:48:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id 467b239f-abec-452e-942b-93b5994c7bfb):
2025-07-01 12:48:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 467b239f-abec-452e-942b-93b5994c7bfb, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 4, 'workflow_status': 'running'}
2025-07-01 12:48:22 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=standard, execution_type=agent)
2025-07-01 12:48:22 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-07-01 12:48:22 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-07-01 12:48:22 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-07-01 12:48:22 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-07-01 12:48:23 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 12:48:23 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 12:48:23 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 12:48:23 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 12:48:23 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 12:48:23 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] '}
2025-07-01 12:48:23 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 12:48:23 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] 
2025-07-01 12:48:23 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  (type: <class 'str'>)
2025-07-01 12:48:23 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 12:48:23 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 12:48:23 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-07-01 12:48:23 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-07-01 12:48:23 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] '}
2025-07-01 12:48:23 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 12:48:23 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] 
2025-07-01 12:48:23 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  (type: <class 'str'>)
2025-07-01 12:48:23 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 12:48:23 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 12:48:23 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → query via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] 
2025-07-01 12:48:23 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-07-01 12:48:23 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-07-01 12:48:23 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-07-01 12:48:23 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with empty collection: {}
2025-07-01 12:48:23 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_type = component
2025-07-01 12:48:23 - TransitionHandler - DEBUG - 📌 Added static parameter: execution_type = response
2025-07-01 12:48:23 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_config = {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}
2025-07-01 12:48:23 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] ', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 12:48:23 - TransitionHandler - DEBUG - tool Parameters: {'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] ', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 12:48:23 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] ', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 12:48:23 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id 467b239f-abec-452e-942b-93b5994c7bfb):
2025-07-01 12:48:23 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 467b239f-abec-452e-942b-93b5994c7bfb, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 5, 'workflow_status': 'running'}
2025-07-01 12:48:23 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 39e1519f-d600-4fc4-83b2-965185382264) with correlation_id: 467b239f-abec-452e-942b-93b5994c7bfb, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-07-01 12:48:23 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-07-01 12:48:23 - AgentExecutor - DEBUG - Added correlation_id 467b239f-abec-452e-942b-93b5994c7bfb to payload
2025-07-01 12:48:23 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '39e1519f-d600-4fc4-83b2-965185382264', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': '467b239f-abec-452e-942b-93b5994c7bfb', 'agent_type': 'component', 'execution_type': 'response', 'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] ', 'variables': {}, 'agent_config': {'id': '88155c8d-793f-4f44-96b6-2e1413818454', 'name': 'AI Agent', 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-07-01 12:48:23 - AgentExecutor - DEBUG - Request 39e1519f-d600-4fc4-83b2-965185382264 sent successfully using provided producer.
2025-07-01 12:48:23 - AgentExecutor - DEBUG - Waiting for single response result for request 39e1519f-d600-4fc4-83b2-965185382264...
2025-07-01 12:48:27 - AgentExecutor - DEBUG - Result consumer received message: Offset=24519
2025-07-01 12:48:27 - AgentExecutor - DEBUG - Processing result payload: {'run_id': '39e1519f-d600-4fc4-83b2-965185382264', 'session_id': '39e1519f-d600-4fc4-83b2-965185382264', 'event_type': None, 'agent_response': {'content': '0', 'source': 'ai_agent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'TextMessage', 'metadata': {}}, 'success': True, 'message': 'Message processed successfully', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'component', 'request_id': '39e1519f-d600-4fc4-83b2-965185382264', 'error_code': None, 'details': None}
2025-07-01 12:48:27 - AgentExecutor - DEBUG - Agent response extracted: {'content': '0', 'source': 'ai_agent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'TextMessage', 'metadata': {}}
2025-07-01 12:48:27 - AgentExecutor - DEBUG - Content extracted: 0...
2025-07-01 12:48:27 - AgentExecutor - DEBUG - Received valid result for request_id 39e1519f-d600-4fc4-83b2-965185382264
2025-07-01 12:48:27 - AgentExecutor - DEBUG - Result consumer received message: Offset=24520
2025-07-01 12:48:27 - AgentExecutor - DEBUG - Processing result payload: {'run_id': '39e1519f-d600-4fc4-83b2-965185382264', 'session_id': '39e1519f-d600-4fc4-83b2-965185382264', 'event_type': None, 'agent_response': {'content': 'Error: Connection error.'}, 'success': False, 'message': 'Message processed successfully', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'component', 'request_id': '39e1519f-d600-4fc4-83b2-965185382264'}
2025-07-01 12:48:27 - AgentExecutor - WARNING - Received connection error for request 39e1519f-d600-4fc4-83b2-965185382264, but continuing to wait for potential success response
2025-07-01 12:48:27 - AgentExecutor - INFO - Single response received for request 39e1519f-d600-4fc4-83b2-965185382264.
2025-07-01 12:48:27 - TransitionHandler - INFO - Execution result from agent executor: "0"
2025-07-01 12:48:27 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id 467b239f-abec-452e-942b-93b5994c7bfb):
2025-07-01 12:48:27 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 467b239f-abec-452e-942b-93b5994c7bfb, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition Result received.', 'result': {'data': '0', 'data_type': 'string', 'semantic_type': 'string', 'property_name': 'content'}, 'status': 'completed', 'sequence': 6, 'workflow_status': 'running', 'approval_required': False}
2025-07-01 12:48:27 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in memory: {'AgenticAI': {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': '0'}, 'status': 'completed', 'timestamp': 1751354307.281236}}
2025-07-01 12:48:27 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-AgenticAI-*************'
2025-07-01 12:48:28 - RedisManager - DEBUG - Set key 'result:transition-AgenticAI-*************' with TTL of 300 seconds
2025-07-01 12:48:28 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 12:48:28 - StateManager - INFO - Marked transition transition-AgenticAI-************* as completed (was_pending=False, was_waiting=False)
2025-07-01 12:48:28 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'transition-AgenticAI-*************', 'transition-AgenticAI-*************'}
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-AgenticAI-*************
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-AgenticAI-*************:
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-AgenticAI-*************, returning empty list
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-AgenticAI-*************
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-LoopNode-*************']
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-LoopNode-*************
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-LoopNode-*************']
2025-07-01 12:48:28 - TransitionHandler - INFO - Completed transition transition-AgenticAI-************* in 5.16 seconds
2025-07-01 12:48:28 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id 467b239f-abec-452e-942b-93b5994c7bfb):
2025-07-01 12:48:28 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 467b239f-abec-452e-942b-93b5994c7bfb, response: {'result': 'Completed transition in 5.16 seconds', 'message': 'Transition completed in 5.16 seconds', 'transition_id': 'transition-AgenticAI-*************', 'status': 'time_logged', 'sequence': 7, 'workflow_status': 'running'}
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-AgenticAI-*************: ['transition-LoopNode-*************']
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-07-01 12:48:28 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-LoopNode-*************']]
2025-07-01 12:48:28 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-*************: ['transition-LoopNode-*************']
2025-07-01 12:48:28 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-01 12:48:28 - EnhancedWorkflowEngine - INFO - Transition transition-AgenticAI-************* completed successfully: 1 next transitions
2025-07-01 12:48:28 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-LoopNode-*************']
2025-07-01 12:48:28 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-LoopNode-*************']
2025-07-01 12:48:28 - EnhancedWorkflowEngine - INFO - Adding transition transition-LoopNode-************* to pending (all dependencies met)
2025-07-01 12:48:28 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-07-01 12:48:28 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:467b239f-abec-452e-942b-93b5994c7bfb'
2025-07-01 12:48:28 - RedisManager - DEBUG - Set key 'workflow_state:467b239f-abec-452e-942b-93b5994c7bfb' with TTL of 600 seconds
2025-07-01 12:48:28 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 467b239f-abec-452e-942b-93b5994c7bfb. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 12:48:28 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-07-01 12:48:28 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-01 12:48:28 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-07-01 12:48:28 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 12:48:28 - StateManager - INFO - Terminated: False
2025-07-01 12:48:28 - StateManager - INFO - Pending transitions (0): []
2025-07-01 12:48:28 - StateManager - INFO - Waiting transitions (1): ['transition-CombineTextComponent-*************']
2025-07-01 12:48:28 - StateManager - INFO - Completed transitions (2): ['transition-AgenticAI-*************', 'transition-AgenticAI-*************']
2025-07-01 12:48:28 - StateManager - INFO - Results stored for 2 transitions
2025-07-01 12:48:28 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 12:48:28 - StateManager - INFO - Workflow status: active
2025-07-01 12:48:28 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 12:48:28 - StateManager - INFO - Workflow status: active
2025-07-01 12:48:28 - StateManager - INFO - Workflow paused: False
2025-07-01 12:48:28 - StateManager - INFO - ==============================
2025-07-01 12:48:28 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-07-01 12:48:28 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id 467b239f-abec-452e-942b-93b5994c7bfb):
2025-07-01 12:48:28 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 467b239f-abec-452e-942b-93b5994c7bfb, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 8, 'workflow_status': 'running'}
2025-07-01 12:48:28 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=standard, execution_type=loop)
2025-07-01 12:48:28 - LoopExecutor - DEBUG - 🔗 Orchestration engine reference set in LoopExecutor
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-07-01 12:48:28 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-07-01 12:48:28 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-07-01 12:48:29 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 12:48:29 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 12:48:29 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 12:48:29 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 12:48:29 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 12:48:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '0'}
2025-07-01 12:48:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 12:48:29 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: 0
2025-07-01 12:48:29 - WorkflowUtils - DEBUG - Found result.result: 0 (type: <class 'str'>)
2025-07-01 12:48:29 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 12:48:29 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 12:48:29 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-07-01 12:48:29 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-07-01 12:48:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '0'}
2025-07-01 12:48:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 12:48:29 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: 0
2025-07-01 12:48:29 - WorkflowUtils - DEBUG - Found result.result: 0 (type: <class 'str'>)
2025-07-01 12:48:29 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 12:48:29 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 12:48:29 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → start via path 'result': 0
2025-07-01 12:48:29 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-07-01 12:48:29 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-07-01 12:48:29 - WorkflowUtils - DEBUG - Filtering out field 'iteration_list' with empty collection: []
2025-07-01 12:48:29 - WorkflowUtils - DEBUG - Filtering out field 'start' with null/empty value: None
2025-07-01 12:48:29 - WorkflowUtils - INFO - 🧹 Parameter filtering: 14 → 12 fields (2 null/empty fields removed)
2025-07-01 12:48:29 - TransitionHandler - DEBUG - 📌 Added static parameter: source_type = number_range
2025-07-01 12:48:29 - TransitionHandler - DEBUG - 📌 Added static parameter: batch_size = 1
2025-07-01 12:48:29 - TransitionHandler - DEBUG - 📌 Added static parameter: end = 6
2025-07-01 12:48:29 - TransitionHandler - DEBUG - 📌 Added static parameter: step = 1
2025-07-01 12:48:29 - TransitionHandler - DEBUG - 📌 Added static parameter: parallel_execution = False
2025-07-01 12:48:29 - TransitionHandler - DEBUG - 📌 Added static parameter: max_concurrent = 3
2025-07-01 12:48:29 - TransitionHandler - DEBUG - 📌 Added static parameter: preserve_order = True
2025-07-01 12:48:29 - TransitionHandler - DEBUG - 📌 Added static parameter: iteration_timeout = 60
2025-07-01 12:48:29 - TransitionHandler - DEBUG - 📌 Added static parameter: aggregation_type = collect_all
2025-07-01 12:48:29 - TransitionHandler - DEBUG - 📌 Added static parameter: include_metadata = False
2025-07-01 12:48:29 - TransitionHandler - DEBUG - 📌 Added static parameter: on_iteration_error = retry_once
2025-07-01 12:48:29 - TransitionHandler - DEBUG - 📌 Added static parameter: include_errors = True
2025-07-01 12:48:29 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-07-01 12:48:29 - TransitionHandler - DEBUG - tool Parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-07-01 12:48:29 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-07-01 12:48:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id 467b239f-abec-452e-942b-93b5994c7bfb):
2025-07-01 12:48:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 467b239f-abec-452e-942b-93b5994c7bfb, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 9, 'workflow_status': 'running'}
2025-07-01 12:48:29 - LoopExecutor - INFO - 🔄 Starting loop execution for transition transition-LoopNode-*************
2025-07-01 12:48:29 - LoopExecutor - INFO - 🔧 Resolving loop configuration for transition transition-LoopNode-*************
2025-07-01 12:48:31 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 12:48:31 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 12:48:31 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 12:48:31 - LoopExecutor - DEBUG - 📋 Resolved loop configuration: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 1, 'end': 6}, 'step': 1, 'source_type': 'number_range'}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}}
2025-07-01 12:48:31 - StateManager - INFO - Built dependency map for 0 transitions
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - INFO - 🔄 Executing iteration 1/6
2025-07-01 12:48:31 - LoopExecutor - INFO - 🔄 Executing iteration 1 with item: 1
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - DEBUG - 🔄 Executing transitions: ['transition-CombineTextComponent-*************']
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - DEBUG - ✅ Transition transition-CombineTextComponent-************* completed in iteration 0
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - INFO - ✅ Iteration 1 completed successfully
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - INFO - 🔄 Executing iteration 2/6
2025-07-01 12:48:31 - LoopExecutor - INFO - 🔄 Executing iteration 2 with item: 2
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - DEBUG - 🔄 Executing transitions: ['transition-CombineTextComponent-*************']
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - DEBUG - ✅ Transition transition-CombineTextComponent-************* completed in iteration 1
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - INFO - ✅ Iteration 2 completed successfully
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - INFO - 🔄 Executing iteration 3/6
2025-07-01 12:48:31 - LoopExecutor - INFO - 🔄 Executing iteration 3 with item: 3
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - DEBUG - 🔄 Executing transitions: ['transition-CombineTextComponent-*************']
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - DEBUG - ✅ Transition transition-CombineTextComponent-************* completed in iteration 2
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - INFO - ✅ Iteration 3 completed successfully
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - INFO - 🔄 Executing iteration 4/6
2025-07-01 12:48:31 - LoopExecutor - INFO - 🔄 Executing iteration 4 with item: 4
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - DEBUG - 🔄 Executing transitions: ['transition-CombineTextComponent-*************']
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - DEBUG - ✅ Transition transition-CombineTextComponent-************* completed in iteration 3
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - INFO - ✅ Iteration 4 completed successfully
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - INFO - 🔄 Executing iteration 5/6
2025-07-01 12:48:31 - LoopExecutor - INFO - 🔄 Executing iteration 5 with item: 5
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - DEBUG - 🔄 Executing transitions: ['transition-CombineTextComponent-*************']
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - DEBUG - ✅ Transition transition-CombineTextComponent-************* completed in iteration 4
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - INFO - ✅ Iteration 5 completed successfully
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - INFO - 🔄 Executing iteration 6/6
2025-07-01 12:48:31 - LoopExecutor - INFO - 🔄 Executing iteration 6 with item: 6
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - DEBUG - 🔄 Executing transitions: ['transition-CombineTextComponent-*************']
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - DEBUG - ✅ Transition transition-CombineTextComponent-************* completed in iteration 5
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - INFO - ✅ Iteration 6 completed successfully
2025-07-01 12:48:31 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_1d31448d, transition_id: transition-LoopNode-*************
2025-07-01 12:48:31 - LoopExecutor - INFO - 🔄 Aggregating 6 results using strategy: collect_all
2025-07-01 12:48:31 - LoopExecutor - INFO - ✅ Loop execution completed for transition transition-LoopNode-*************
2025-07-01 12:48:31 - TransitionHandler - ERROR - Tool execution failed for tool 'LoopNode' (tool_id: 1) in node 'LoopNode' of transition 'transition-LoopNode-*************': Circular reference detectedTraceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 391, in _execute_standard_or_reflection_transition
    f"Execution result from {execution_type} executor: {json.dumps(execution_result, indent=2)}"
                                                        ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ~~~~~~^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
ValueError: Circular reference detected

2025-07-01 12:48:31 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id 467b239f-abec-452e-942b-93b5994c7bfb):
2025-07-01 12:48:31 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 467b239f-abec-452e-942b-93b5994c7bfb, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Circular reference detected', 'status': 'failed', 'sequence': 10, 'workflow_status': 'running'}
2025-07-01 12:48:31 - TransitionHandler - ERROR - Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Circular reference detected
2025-07-01 12:48:31 - EnhancedWorkflowEngine - DEBUG - Results: [Exception('Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Circular reference detected')]
2025-07-01 12:48:31 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-LoopNode-*************: Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Circular reference detected
2025-07-01 12:48:31 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'Exception'>, is_list: False
2025-07-01 12:48:31 - EnhancedWorkflowEngine - ERROR - Error in execution of transition transition-LoopNode-*************: Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Circular reference detected
2025-07-01 12:48:31 - EnhancedWorkflowEngine - ERROR - Traceback for transition transition-LoopNode-*************: NoneType: None

2025-07-01 12:48:31 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during parallel execution of standard transitions: Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Circular reference detected
2025-07-01 12:48:31 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 391, in _execute_standard_or_reflection_transition
    f"Execution result from {execution_type} executor: {json.dumps(execution_result, indent=2)}"
                                                        ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ~~~~~~^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
ValueError: Circular reference detected

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 109, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 637, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Circular reference detected

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 135, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Circular reference detected

2025-07-01 12:48:31 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during workflow execution: Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Circular reference detected
2025-07-01 12:48:31 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 391, in _execute_standard_or_reflection_transition
    f"Execution result from {execution_type} executor: {json.dumps(execution_result, indent=2)}"
                                                        ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ~~~~~~^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
ValueError: Circular reference detected

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 109, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 637, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Circular reference detected

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 285, in execute
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 135, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Circular reference detected

2025-07-01 12:48:31 - KafkaWorkflowConsumer - ERROR - Exception in workflow execution: Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Circular reference detected
2025-07-01 12:48:31 - KafkaWorkflowConsumer - INFO - Workflow '0c19c070-905e-46ef-9f57-62eb427bf396' final status: failed, result: Exception in workflow '0c19c070-905e-46ef-9f57-62eb427bf396': Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Circular reference detected
2025-07-01 12:48:31 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 467b239f-abec-452e-942b-93b5994c7bfb, response: {'status': 'failed', 'result': "Exception in workflow '0c19c070-905e-46ef-9f57-62eb427bf396': Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Circular reference detected", 'workflow_status': 'failed', 'error': 'Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Circular reference detected', 'error_type': 'Exception'}
2025-07-01 12:48:31 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: 467b239f-abec-452e-942b-93b5994c7bfb 
2025-07-01 12:49:21 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 12:49:21 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 12:49:21 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 12:49:21 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 12:50:21 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 12:50:21 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 12:50:21 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 12:50:21 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 12:51:21 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 12:51:21 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 12:51:21 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 12:51:21 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 12:52:01 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:d7071285-52e0-43d9-8b74-28599accbcce', 'data': b'expired'}
2025-07-01 12:52:01 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:d7071285-52e0-43d9-8b74-28599accbcce'
2025-07-01 12:52:01 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:d7071285-52e0-43d9-8b74-28599accbcce
2025-07-01 12:52:01 - RedisEventListener - DEBUG - Extracted key: workflow_state:d7071285-52e0-43d9-8b74-28599accbcce
2025-07-01 12:52:01 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 12:52:01 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 12:52:01 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: d7071285-52e0-43d9-8b74-28599accbcce
2025-07-01 12:52:01 - RedisEventListener - INFO - Archiving workflow state for workflow: d7071285-52e0-43d9-8b74-28599accbcce
2025-07-01 12:52:05 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-07-01 12:52:06 - RedisEventListener - ERROR - Error handling state DB event: Circular reference detected
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/db_connections/redis_event_listener.py", line 399, in _handle_state_db_event
    self.workflow_state_manager.archive_workflow_state()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/state_manager.py", line 852, in archive_workflow_state
    success = self.postgres_manager.store_workflow_state(
        self.workflow_id, self.workflow_id, state
    )
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/db_connections/postgres_connections.py", line 260, in store_workflow_state
    cursor.execute(
    ~~~~~~~~~~~~~~^
        """
        ^^^
    ...<3 lines>...
        (correlation_id, workflow_id, Json(state_data)),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/orchestration-engine-tNJVEdE_-py3.13/lib/python3.13/site-packages/psycopg2/_json.py", line 78, in getquoted
    s = self.dumps(self.adapted)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/orchestration-engine-tNJVEdE_-py3.13/lib/python3.13/site-packages/psycopg2/_json.py", line 72, in dumps
    return self._dumps(obj)
           ~~~~~~~~~~~^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
ValueError: Circular reference detected
2025-07-01 12:52:21 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 12:52:21 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 12:52:21 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 12:52:21 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 12:53:21 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 12:53:21 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 12:53:21 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 12:53:21 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 12:53:22 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-AgenticAI-*************', 'data': b'expired'}
2025-07-01 12:53:22 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-AgenticAI-*************'
2025-07-01 12:53:22 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-AgenticAI-*************
2025-07-01 12:53:22 - RedisEventListener - DEBUG - Extracted key: result:transition-AgenticAI-*************
2025-07-01 12:53:22 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 12:53:22 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 12:53:22 - RedisEventListener - INFO - Detected expired event for result of transition: transition-AgenticAI-*************
2025-07-01 12:53:22 - RedisEventListener - INFO - Archiving result for transition: transition-AgenticAI-*************
2025-07-01 12:53:22 - StateManager - DEBUG - Attempting to archive result for transition transition-AgenticAI-*************
2025-07-01 12:53:22 - StateManager - DEBUG - Provided result: False
2025-07-01 12:53:23 - StateManager - DEBUG - Trying to get result from Redis for transition transition-AgenticAI-*************
2025-07-01 12:53:23 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-*************
2025-07-01 12:53:23 - StateManager - DEBUG - Trying to get result from memory for transition transition-AgenticAI-*************
2025-07-01 12:53:23 - StateManager - DEBUG - Found result in memory for transition transition-AgenticAI-*************
2025-07-01 12:53:23 - StateManager - DEBUG - Archiving result to PostgreSQL for transition transition-AgenticAI-*************
2025-07-01 12:53:23 - PostgresManager - DEBUG - Attempting to store transition result for transition-AgenticAI-************* in correlation 467b239f-abec-452e-942b-93b5994c7bfb
2025-07-01 12:53:23 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-07-01 12:53:23 - PostgresManager - DEBUG - Result data: {'AgenticAI': {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] '}, 'status': 'completed', 'timestamp': 1751354300.851959}}
2025-07-01 12:53:24 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-07-01 12:53:25 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-07-01 12:53:25 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-07-01 12:53:25 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-07-01 12:53:25 - Main - ERROR - Shutting down due to keyboard interrupt...
