2025-07-01 17:45:20 - Main - INFO - Starting Server
2025-07-01 17:45:20 - Main - INFO - Connection at: **************:9092
2025-07-01 17:45:20 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-01 17:45:20 - NodeExecutor - INFO - NodeExecutor initialized.
2025-07-01 17:45:20 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-01 17:45:20 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-07-01 17:45:20 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 17:45:22 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 17:45:22 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 17:45:24 - <PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 17:45:25 - PostgresManager - INFO - PostgreSQL connection pool created
2025-07-01 17:45:25 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-07-01 17:45:28 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-07-01 17:45:29 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-07-01 17:45:29 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 17:45:30 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 17:45:30 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 17:45:32 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 17:45:32 - RedisEventListener - INFO - Starting Redis event listener thread
2025-07-01 17:45:32 - RedisEventListener - INFO - Redis event listener started
2025-07-01 17:45:32 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-07-01 17:45:32 - StateManager - DEBUG - Using provided database connections
2025-07-01 17:45:32 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 17:45:32 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 17:45:32 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 17:45:33 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-07-01 17:45:33 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 17:45:33 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 17:45:33 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-07-01 17:45:33 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-07-01 17:45:33 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-07-01 17:45:33 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-07-01 17:45:36 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-07-01 17:45:36 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-07-01 17:45:36 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-07-01 17:45:41 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-07-01 17:45:44 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-AgenticAI-1750505490787_iteration_4', 'data': b'expired'}
2025-07-01 17:45:44 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-AgenticAI-1750505490787_iteration_4'
2025-07-01 17:45:44 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-AgenticAI-1750505490787_iteration_4
2025-07-01 17:45:44 - RedisEventListener - DEBUG - Extracted key: result:transition-AgenticAI-1750505490787_iteration_4
2025-07-01 17:45:44 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 17:45:44 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 17:45:44 - RedisEventListener - INFO - Detected expired event for result of transition: transition-AgenticAI-1750505490787_iteration_4
2025-07-01 17:45:44 - RedisEventListener - INFO - Archiving result for transition: transition-AgenticAI-1750505490787_iteration_4
2025-07-01 17:45:44 - StateManager - DEBUG - Attempting to archive result for transition transition-AgenticAI-1750505490787_iteration_4
2025-07-01 17:45:45 - StateManager - DEBUG - Provided result: False
2025-07-01 17:45:46 - StateManager - DEBUG - Trying to get result from Redis for transition transition-AgenticAI-1750505490787_iteration_4
2025-07-01 17:45:46 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-1750505490787_iteration_4
2025-07-01 17:45:46 - StateManager - DEBUG - Trying to get result from memory for transition transition-AgenticAI-1750505490787_iteration_4
2025-07-01 17:45:46 - StateManager - DEBUG - No result found in memory for transition transition-AgenticAI-1750505490787_iteration_4
2025-07-01 17:45:46 - StateManager - DEBUG - Available transition results in memory: []
2025-07-01 17:45:46 - StateManager - WARNING - No result found to archive for transition transition-AgenticAI-1750505490787_iteration_4
2025-07-01 17:45:48 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-07-01 17:45:48 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-07-01 17:45:48 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-07-01 17:45:52 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-AgenticAI-1751001471087_iteration_4', 'data': b'expired'}
2025-07-01 17:45:52 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-AgenticAI-1751001471087_iteration_4'
2025-07-01 17:45:52 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-AgenticAI-1751001471087_iteration_4
2025-07-01 17:45:52 - RedisEventListener - DEBUG - Extracted key: result:transition-AgenticAI-1751001471087_iteration_4
2025-07-01 17:45:52 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 17:45:52 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 17:45:52 - RedisEventListener - INFO - Detected expired event for result of transition: transition-AgenticAI-1751001471087_iteration_4
2025-07-01 17:45:52 - RedisEventListener - INFO - Archiving result for transition: transition-AgenticAI-1751001471087_iteration_4
2025-07-01 17:45:52 - StateManager - DEBUG - Attempting to archive result for transition transition-AgenticAI-1751001471087_iteration_4
2025-07-01 17:45:53 - StateManager - DEBUG - Provided result: False
2025-07-01 17:45:53 - StateManager - DEBUG - Trying to get result from Redis for transition transition-AgenticAI-1751001471087_iteration_4
2025-07-01 17:45:54 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-1751001471087_iteration_4
2025-07-01 17:45:54 - StateManager - DEBUG - Trying to get result from memory for transition transition-AgenticAI-1751001471087_iteration_4
2025-07-01 17:45:54 - StateManager - DEBUG - No result found in memory for transition transition-AgenticAI-1751001471087_iteration_4
2025-07-01 17:45:54 - StateManager - DEBUG - Available transition results in memory: []
2025-07-01 17:45:54 - StateManager - WARNING - No result found to archive for transition transition-AgenticAI-1751001471087_iteration_4
2025-07-01 17:45:55 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-07-01 17:45:55 - NodeExecutor - INFO - Background result consumer loop started.
2025-07-01 17:45:55 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-07-01 17:46:00 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-AgenticAI-1751001474606_iteration_4', 'data': b'expired'}
2025-07-01 17:46:00 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-AgenticAI-1751001474606_iteration_4'
2025-07-01 17:46:00 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-AgenticAI-1751001474606_iteration_4
2025-07-01 17:46:00 - RedisEventListener - DEBUG - Extracted key: result:transition-AgenticAI-1751001474606_iteration_4
2025-07-01 17:46:00 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 17:46:00 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 17:46:00 - RedisEventListener - INFO - Detected expired event for result of transition: transition-AgenticAI-1751001474606_iteration_4
2025-07-01 17:46:00 - RedisEventListener - INFO - Archiving result for transition: transition-AgenticAI-1751001474606_iteration_4
2025-07-01 17:46:00 - StateManager - DEBUG - Attempting to archive result for transition transition-AgenticAI-1751001474606_iteration_4
2025-07-01 17:46:01 - StateManager - DEBUG - Provided result: False
2025-07-01 17:46:01 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-07-01 17:46:01 - AgentExecutor - INFO - Background result consumer loop started.
2025-07-01 17:46:01 - StateManager - DEBUG - Trying to get result from Redis for transition transition-AgenticAI-1751001474606_iteration_4
2025-07-01 17:46:02 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-1751001474606_iteration_4
2025-07-01 17:46:02 - StateManager - DEBUG - Trying to get result from memory for transition transition-AgenticAI-1751001474606_iteration_4
2025-07-01 17:46:02 - StateManager - DEBUG - No result found in memory for transition transition-AgenticAI-1751001474606_iteration_4
2025-07-01 17:46:02 - StateManager - DEBUG - Available transition results in memory: []
2025-07-01 17:46:02 - StateManager - WARNING - No result found to archive for transition transition-AgenticAI-1751001474606_iteration_4
2025-07-01 17:46:22 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-CombineTextComponent-1751005393963_iteration_5', 'data': b'expired'}
2025-07-01 17:46:22 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-CombineTextComponent-1751005393963_iteration_5'
2025-07-01 17:46:22 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-CombineTextComponent-1751005393963_iteration_5
2025-07-01 17:46:22 - RedisEventListener - DEBUG - Extracted key: result:transition-CombineTextComponent-1751005393963_iteration_5
2025-07-01 17:46:22 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 17:46:22 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 17:46:22 - RedisEventListener - INFO - Detected expired event for result of transition: transition-CombineTextComponent-1751005393963_iteration_5
2025-07-01 17:46:22 - RedisEventListener - INFO - Archiving result for transition: transition-CombineTextComponent-1751005393963_iteration_5
2025-07-01 17:46:22 - StateManager - DEBUG - Attempting to archive result for transition transition-CombineTextComponent-1751005393963_iteration_5
2025-07-01 17:46:23 - StateManager - DEBUG - Provided result: False
2025-07-01 17:46:23 - StateManager - DEBUG - Trying to get result from Redis for transition transition-CombineTextComponent-1751005393963_iteration_5
2025-07-01 17:46:23 - StateManager - DEBUG - No result found in Redis for transition transition-CombineTextComponent-1751005393963_iteration_5
2025-07-01 17:46:23 - StateManager - DEBUG - Trying to get result from memory for transition transition-CombineTextComponent-1751005393963_iteration_5
2025-07-01 17:46:23 - StateManager - DEBUG - No result found in memory for transition transition-CombineTextComponent-1751005393963_iteration_5
2025-07-01 17:46:23 - StateManager - DEBUG - Available transition results in memory: []
2025-07-01 17:46:23 - StateManager - WARNING - No result found to archive for transition transition-CombineTextComponent-1751005393963_iteration_5
2025-07-01 17:46:29 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 17:46:29 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 17:46:29 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 17:46:29 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 17:46:37 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-AgenticAI-1750505490787_iteration_5', 'data': b'expired'}
2025-07-01 17:46:37 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-AgenticAI-1750505490787_iteration_5'
2025-07-01 17:46:37 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-AgenticAI-1750505490787_iteration_5
2025-07-01 17:46:37 - RedisEventListener - DEBUG - Extracted key: result:transition-AgenticAI-1750505490787_iteration_5
2025-07-01 17:46:37 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 17:46:37 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 17:46:37 - RedisEventListener - INFO - Detected expired event for result of transition: transition-AgenticAI-1750505490787_iteration_5
2025-07-01 17:46:37 - RedisEventListener - INFO - Archiving result for transition: transition-AgenticAI-1750505490787_iteration_5
2025-07-01 17:46:37 - StateManager - DEBUG - Attempting to archive result for transition transition-AgenticAI-1750505490787_iteration_5
2025-07-01 17:46:37 - StateManager - DEBUG - Provided result: False
2025-07-01 17:46:38 - StateManager - DEBUG - Trying to get result from Redis for transition transition-AgenticAI-1750505490787_iteration_5
2025-07-01 17:46:39 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-1750505490787_iteration_5
2025-07-01 17:46:39 - StateManager - DEBUG - Trying to get result from memory for transition transition-AgenticAI-1750505490787_iteration_5
2025-07-01 17:46:39 - StateManager - DEBUG - No result found in memory for transition transition-AgenticAI-1750505490787_iteration_5
2025-07-01 17:46:39 - StateManager - DEBUG - Available transition results in memory: []
2025-07-01 17:46:39 - StateManager - WARNING - No result found to archive for transition transition-AgenticAI-1750505490787_iteration_5
2025-07-01 17:46:48 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-AgenticAI-1751001471087_iteration_5', 'data': b'expired'}
2025-07-01 17:46:48 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-AgenticAI-1751001471087_iteration_5'
2025-07-01 17:46:48 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-AgenticAI-1751001471087_iteration_5
2025-07-01 17:46:48 - RedisEventListener - DEBUG - Extracted key: result:transition-AgenticAI-1751001471087_iteration_5
2025-07-01 17:46:48 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 17:46:48 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 17:46:48 - RedisEventListener - INFO - Detected expired event for result of transition: transition-AgenticAI-1751001471087_iteration_5
2025-07-01 17:46:48 - RedisEventListener - INFO - Archiving result for transition: transition-AgenticAI-1751001471087_iteration_5
2025-07-01 17:46:48 - StateManager - DEBUG - Attempting to archive result for transition transition-AgenticAI-1751001471087_iteration_5
2025-07-01 17:46:49 - StateManager - DEBUG - Provided result: False
2025-07-01 17:46:49 - StateManager - DEBUG - Trying to get result from Redis for transition transition-AgenticAI-1751001471087_iteration_5
2025-07-01 17:46:50 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-1751001471087_iteration_5
2025-07-01 17:46:50 - StateManager - DEBUG - Trying to get result from memory for transition transition-AgenticAI-1751001471087_iteration_5
2025-07-01 17:46:50 - StateManager - DEBUG - No result found in memory for transition transition-AgenticAI-1751001471087_iteration_5
2025-07-01 17:46:50 - StateManager - DEBUG - Available transition results in memory: []
2025-07-01 17:46:50 - StateManager - WARNING - No result found to archive for transition transition-AgenticAI-1751001471087_iteration_5
2025-07-01 17:46:58 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-AgenticAI-1751001474606_iteration_5', 'data': b'expired'}
2025-07-01 17:46:59 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-AgenticAI-1751001474606_iteration_5'
2025-07-01 17:46:59 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-AgenticAI-1751001474606_iteration_5
2025-07-01 17:46:59 - RedisEventListener - DEBUG - Extracted key: result:transition-AgenticAI-1751001474606_iteration_5
2025-07-01 17:46:59 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 17:46:59 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 17:46:59 - RedisEventListener - INFO - Detected expired event for result of transition: transition-AgenticAI-1751001474606_iteration_5
2025-07-01 17:46:59 - RedisEventListener - INFO - Archiving result for transition: transition-AgenticAI-1751001474606_iteration_5
2025-07-01 17:46:59 - StateManager - DEBUG - Attempting to archive result for transition transition-AgenticAI-1751001474606_iteration_5
2025-07-01 17:46:59 - StateManager - DEBUG - Provided result: False
2025-07-01 17:47:00 - StateManager - DEBUG - Trying to get result from Redis for transition transition-AgenticAI-1751001474606_iteration_5
2025-07-01 17:47:00 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-1751001474606_iteration_5
2025-07-01 17:47:00 - StateManager - DEBUG - Trying to get result from memory for transition transition-AgenticAI-1751001474606_iteration_5
2025-07-01 17:47:00 - StateManager - DEBUG - No result found in memory for transition transition-AgenticAI-1751001474606_iteration_5
2025-07-01 17:47:00 - StateManager - DEBUG - Available transition results in memory: []
2025-07-01 17:47:00 - StateManager - WARNING - No result found to archive for transition transition-AgenticAI-1751001474606_iteration_5
2025-07-01 17:47:22 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-LoopNode-1751099881332', 'data': b'expired'}
2025-07-01 17:47:22 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-LoopNode-1751099881332'
2025-07-01 17:47:22 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-LoopNode-1751099881332
2025-07-01 17:47:22 - RedisEventListener - DEBUG - Extracted key: result:transition-LoopNode-1751099881332
2025-07-01 17:47:22 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 17:47:22 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 17:47:22 - RedisEventListener - INFO - Detected expired event for result of transition: transition-LoopNode-1751099881332
2025-07-01 17:47:22 - RedisEventListener - INFO - Archiving result for transition: transition-LoopNode-1751099881332
2025-07-01 17:47:22 - StateManager - DEBUG - Attempting to archive result for transition transition-LoopNode-1751099881332
2025-07-01 17:47:23 - StateManager - DEBUG - Provided result: False
2025-07-01 17:47:23 - StateManager - DEBUG - Trying to get result from Redis for transition transition-LoopNode-1751099881332
2025-07-01 17:47:24 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-1751099881332
2025-07-01 17:47:24 - StateManager - DEBUG - Trying to get result from memory for transition transition-LoopNode-1751099881332
2025-07-01 17:47:24 - StateManager - DEBUG - No result found in memory for transition transition-LoopNode-1751099881332
2025-07-01 17:47:24 - StateManager - DEBUG - Available transition results in memory: []
2025-07-01 17:47:24 - StateManager - WARNING - No result found to archive for transition transition-LoopNode-1751099881332
2025-07-01 17:47:29 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 17:47:29 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 17:47:29 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 17:47:29 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 17:47:37 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-CombineTextComponent-1751005393963', 'data': b'expired'}
2025-07-01 17:47:37 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-CombineTextComponent-1751005393963'
2025-07-01 17:47:37 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-CombineTextComponent-1751005393963
2025-07-01 17:47:37 - RedisEventListener - DEBUG - Extracted key: result:transition-CombineTextComponent-1751005393963
2025-07-01 17:47:37 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 17:47:37 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 17:47:37 - RedisEventListener - INFO - Detected expired event for result of transition: transition-CombineTextComponent-1751005393963
2025-07-01 17:47:37 - RedisEventListener - INFO - Archiving result for transition: transition-CombineTextComponent-1751005393963
2025-07-01 17:47:37 - StateManager - DEBUG - Attempting to archive result for transition transition-CombineTextComponent-1751005393963
2025-07-01 17:47:38 - StateManager - DEBUG - Provided result: False
2025-07-01 17:47:38 - StateManager - DEBUG - Trying to get result from Redis for transition transition-CombineTextComponent-1751005393963
2025-07-01 17:47:39 - StateManager - DEBUG - No result found in Redis for transition transition-CombineTextComponent-1751005393963
2025-07-01 17:47:39 - StateManager - DEBUG - Trying to get result from memory for transition transition-CombineTextComponent-1751005393963
2025-07-01 17:47:39 - StateManager - DEBUG - No result found in memory for transition transition-CombineTextComponent-1751005393963
2025-07-01 17:47:39 - StateManager - DEBUG - Available transition results in memory: []
2025-07-01 17:47:39 - StateManager - WARNING - No result found to archive for transition transition-CombineTextComponent-1751005393963
2025-07-01 17:47:39 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-MergeDataComponent-1751005477464', 'data': b'expired'}
2025-07-01 17:47:39 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-MergeDataComponent-1751005477464'
2025-07-01 17:47:39 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-MergeDataComponent-1751005477464
2025-07-01 17:47:39 - RedisEventListener - DEBUG - Extracted key: result:transition-MergeDataComponent-1751005477464
2025-07-01 17:47:39 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 17:47:39 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 17:47:39 - RedisEventListener - INFO - Detected expired event for result of transition: transition-MergeDataComponent-1751005477464
2025-07-01 17:47:39 - RedisEventListener - INFO - Archiving result for transition: transition-MergeDataComponent-1751005477464
2025-07-01 17:47:39 - StateManager - DEBUG - Attempting to archive result for transition transition-MergeDataComponent-1751005477464
2025-07-01 17:47:40 - StateManager - DEBUG - Provided result: False
2025-07-01 17:47:40 - StateManager - DEBUG - Trying to get result from Redis for transition transition-MergeDataComponent-1751005477464
2025-07-01 17:47:41 - StateManager - DEBUG - No result found in Redis for transition transition-MergeDataComponent-1751005477464
2025-07-01 17:47:41 - StateManager - DEBUG - Trying to get result from memory for transition transition-MergeDataComponent-1751005477464
2025-07-01 17:47:41 - StateManager - DEBUG - No result found in memory for transition transition-MergeDataComponent-1751005477464
2025-07-01 17:47:41 - StateManager - DEBUG - Available transition results in memory: []
2025-07-01 17:47:41 - StateManager - WARNING - No result found to archive for transition transition-MergeDataComponent-1751005477464
2025-07-01 17:48:04 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-07-01 17:48:04 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-07-01 17:48:04 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-07-01 17:48:04 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-07-01 17:48:04 - Main - ERROR - Shutting down due to keyboard interrupt...
