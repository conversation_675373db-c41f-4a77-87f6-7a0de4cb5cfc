"""
Loop Body Executor - Manages execution of loop body transition chains.

This module handles the execution of individual loop iterations by managing
the transition chain that forms the loop body, following the main loop
execution patterns with proper state tracking.
"""

import asyncio
import uuid
from typing import Dict, List, Any, Optional, Set
from app.utils.enhanced_logger import get_logger

logger = get_logger("LoopBodyExecutor")


class LoopBodyExecutor:
    """
    Executes loop body transition chains for individual iterations.
    
    This class manages the execution of the transition chain that forms the
    loop body, handling entry transitions, dependency tracking, and exit
    transition detection following the main orchestration engine patterns.
    """
    
    def __init__(
        self,
        state_manager,
        transition_handler,
        transitions_by_id: Dict[str, Any],
        loop_state_manager,
        logger
    ):
        """
        Initialize the loop body executor.
        
        Args:
            state_manager: Main workflow state manager
            transition_handler: Main transition handler instance
            transitions_by_id: Dictionary of all transitions in the workflow
            loop_state_manager: Loop-specific state manager
            logger: Logger instance
        """
        self.state_manager = state_manager
        self.transition_handler = transition_handler
        self.transitions_by_id = transitions_by_id
        self.loop_state_manager = loop_state_manager
        self.logger = logger
        
        # Execution tracking
        self.current_iteration_index = None
        self.current_iteration_item = None
        self.iteration_context = {}
        
    async def execute_iteration(
        self,
        iteration_index: int,
        iteration_item: Any,
        loop_config: Dict[str, Any],
        output_routing: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute a single loop iteration.
        
        Args:
            iteration_index: Index of the current iteration
            iteration_item: The item being processed in this iteration
            loop_config: Complete loop configuration
            output_routing: Output routing configuration
            
        Returns:
            Result of the iteration execution
        """
        self.logger.info(f"🔄 Executing iteration {iteration_index + 1} with item: {iteration_item}")
        
        try:
            # Set up iteration context
            self.current_iteration_index = iteration_index
            self.current_iteration_item = iteration_item
            self.iteration_context = {
                "iteration_index": iteration_index,
                "iteration_item": iteration_item,
                "loop_id": self.loop_state_manager.loop_id,
                "transition_id": self.loop_state_manager.transition_id
            }
            
            # Start iteration in state manager
            await self.loop_state_manager.start_iteration(iteration_index, iteration_item)
            
            # Execute the loop body chain
            result = await self._execute_loop_body_chain(loop_config, output_routing)
            
            # Complete iteration in state manager
            await self.loop_state_manager.complete_iteration(
                iteration_index, result, "completed"
            )
            
            self.logger.info(f"✅ Iteration {iteration_index + 1} completed successfully")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Iteration {iteration_index + 1} failed: {str(e)}")
            
            # Handle iteration error
            error_result = await self._handle_iteration_error(e, loop_config)
            
            # Mark iteration as failed
            await self.loop_state_manager.complete_iteration(
                iteration_index, error_result, "failed"
            )
            
            return error_result
    
    async def _execute_loop_body_chain(
        self,
        loop_config: Dict[str, Any],
        output_routing: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute the loop body transition chain using the workflow's natural execution flow.

        This method:
        1. Starts with entry transitions
        2. Uses _execute_transition_with_tracking to get next transitions
        3. Continues until exit transitions are reached
        4. Aggregates results for the iteration
        """
        loop_body_config = loop_config.get("loop_body_configuration", {})
        entry_transitions = loop_body_config.get("entry_transitions", [])
        exit_transitions = loop_body_config.get("exit_transitions", [])

        if not entry_transitions:
            raise ValueError("No entry transitions defined for loop body")

        # Prepare iteration data for transitions
        iteration_data = await self._prepare_iteration_data()

        iteration_results = {}

        # Initialize loop state for this iteration
        await self.loop_state_manager.start_iteration(self.current_iteration_index, self.current_iteration_item)
        reached_exit = False

        # Start with entry transitions
        current_transitions = entry_transitions.copy()
        self.logger.info(f"🚀 Starting loop body execution with entry transitions: {current_transitions}")

        # Execute transitions following the workflow's natural flow with state management
        while current_transitions and not reached_exit:
            next_batch = []

            for transition_id in current_transitions:
                self.logger.info(f"🔄 Executing transition: {transition_id}")

                try:
                    # Execute transition using existing workflow method
                    result = await self._execute_single_transition_with_tracking(
                        transition_id, iteration_data
                    )

                    # Store the result
                    iteration_results[transition_id] = result.get("result")

                    # Update loop state manager using the existing handle_transition_completion method
                    next_transitions = await self.loop_state_manager.handle_transition_completion(
                        transition_id, self.current_iteration_index, result.get("result")
                    )

                    self.logger.debug(f"🔄 Transition {transition_id} returned next transitions: {next_transitions}")

                    # Add next transitions to the batch for next iteration
                    # Note: handle_transition_completion already checked dependencies and only returns ready transitions
                    for next_id in next_transitions:
                        if next_id not in next_batch:
                            next_batch.append(next_id)

                    # Check if we've reached an exit transition
                    if transition_id in exit_transitions:
                        self.logger.info(f"🏁 Reached exit transition: {transition_id}")
                        reached_exit = True

                except Exception as e:
                    self.logger.error(f"❌ Failed to execute transition {transition_id}: {str(e)}")
                    raise

            # Continue with the next batch of transitions
            current_transitions = next_batch

        self.logger.info(f"🏁 Loop body execution completed for iteration {self.current_iteration_index}")

        # Format final result for this iteration
        return await self._format_iteration_result(iteration_results, output_routing)

    async def _execute_transition_batch(
        self,
        transition_ids: List[str],
        iteration_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute a batch of transitions for the current iteration.
        
        This method uses the main transition handler's _execute_transitions
        method with proper tracking and state management.
        """
        batch_results = {}
        
        for transition_id in transition_ids:
            try:
                # Get the transition configuration
                transition_config = self.transitions_by_id.get(transition_id)
                if not transition_config:
                    self.logger.warning(f"Transition {transition_id} not found in transitions_by_id")
                    continue
                
                # Create iteration-specific transition ID for tracking
                iteration_transition_id = self.loop_state_manager._get_iteration_transition_id(
                    transition_id, self.current_iteration_index
                )
                
                # Prepare transition for execution
                await self._prepare_transition_for_execution(
                    transition_id, iteration_transition_id, iteration_data
                )
                
                # Execute the transition using the main transition handler
                result = await self._execute_single_transition(
                    transition_id, iteration_transition_id, transition_config, iteration_data
                )
                
                # Store result
                batch_results[transition_id] = result

                self.logger.debug(f"✅ Transition {transition_id} completed in iteration {self.current_iteration_index}")

                # Note: Transition completion is handled by the calling method
                # which will call loop_state_manager.mark_transition_completed()
                
            except Exception as e:
                self.logger.error(f"❌ Transition {transition_id} failed in iteration {self.current_iteration_index}: {str(e)}")
                batch_results[transition_id] = {
                    "error": str(e),
                    "status": "failed"
                }
        
        return batch_results
    
    async def _execute_single_transition(
        self,
        transition_id: str,
        iteration_transition_id: str,
        transition_config: Dict[str, Any],
        iteration_data: Dict[str, Any]
    ) -> Any:
        """
        Execute a single transition using the main transition handler.
        
        This method integrates with the main transition handler's execution
        logic while maintaining iteration-specific context.
        """
        # Add iteration context to the transition execution
        enhanced_transition_config = transition_config.copy()
        enhanced_transition_config["iteration_context"] = self.iteration_context
        
        # Use the main transition handler's execution method with proper tracking
        try:
            # Call the main execution method with tracking - this will properly execute the transition
            # through the normal workflow execution path and store results in the main state manager
            result = await self.transition_handler._execute_transition_with_tracking(enhanced_transition_config)

            # The result should be a list of next transition IDs
            # For loop body execution, we need to return the execution result
            # Get the actual result from the state manager
            actual_result = self.state_manager.get_transition_result(iteration_transition_id)

            return {
                "status": "completed",
                "result": actual_result,
                "next_transitions": result if isinstance(result, list) else []
            }
        except Exception as e:
            self.logger.error(f"❌ Failed to execute transition {iteration_transition_id}: {str(e)}")
            # Fallback: use the tool execution method directly
            return await self._execute_transition_tool(enhanced_transition_config, iteration_data)
    
    async def _execute_transition_tool(
        self,
        transition_config: Dict[str, Any],
        iteration_data: Dict[str, Any]
    ) -> Any:
        """
        Fallback method to execute transition tool directly.
        
        This is used when the main transition handler's _execute_transitions
        method is not available or accessible.
        """
        node_info = transition_config.get("node_info", {})
        tools_to_use = node_info.get("tools_to_use", [])
        
        if not tools_to_use:
            return {"status": "no_tools", "result": None}
        
        # Execute the first tool (simplified approach)
        tool_config = tools_to_use[0]
        tool_name = tool_config.get("tool_name")
        tool_params = tool_config.get("tool_params", {})
        
        # Add iteration data to tool parameters
        enhanced_params = tool_params.copy()
        enhanced_params["iteration_data"] = iteration_data
        enhanced_params["iteration_context"] = self.iteration_context
        
        # Use the transition handler's tool execution if available
        if hasattr(self.transition_handler, 'execute_tool'):
            return await self.transition_handler.execute_tool(
                tool_name=tool_name,
                tool_parameters=enhanced_params,
                transition_id=transition_config.get("id"),
                input_data=iteration_data
            )
        else:
            # Return a placeholder result
            return {
                "tool_name": tool_name,
                "parameters": enhanced_params,
                "iteration_context": self.iteration_context,
                "status": "executed"
            }
    
    async def _prepare_iteration_data(self) -> Dict[str, Any]:
        """
        Prepare data context for the current iteration.
        
        This includes the current item, iteration index, and any
        previous iteration results that should be available.
        """
        return {
            "current_item": self.current_iteration_item,
            "iteration_index": self.current_iteration_index,
            "loop_id": self.loop_state_manager.loop_id,
            "loop_context": self.iteration_context
        }
    
    async def _prepare_transition_for_execution(
        self,
        transition_id: str,
        iteration_transition_id: str,
        iteration_data: Dict[str, Any]
    ) -> None:
        """
        Prepare a transition for execution within the iteration context.
        
        This method sets up the necessary state in the main state manager
        to track the iteration-specific transition execution.
        """
        # Add to pending transitions in main state manager
        self.state_manager.pending_transitions.add(iteration_transition_id)
        
        # Store iteration context for this transition
        if hasattr(self.state_manager, 'store_transition_context'):
            self.state_manager.store_transition_context(
                iteration_transition_id, self.iteration_context
            )
    
    async def _find_next_transitions_in_chain(
        self,
        completed_transitions: List[str],
        results: Dict[str, Any]
    ) -> List[str]:
        """
        Find the next transitions to execute in the loop body chain.
        
        This method uses the workflow's routing logic to determine
        which transitions should execute next based on the completed
        transitions and their results.
        """
        next_transitions = []
        
        # This is a simplified implementation
        # In a real scenario, this would use the workflow's dependency
        # and routing system to determine next transitions
        
        for transition_id in completed_transitions:
            # Check if this transition has dependent transitions
            if hasattr(self.state_manager, 'get_dependent_transitions'):
                dependents = self.state_manager.get_dependent_transitions(transition_id)
                next_transitions.extend(dependents)
        
        # Remove duplicates and filter out already completed transitions
        next_transitions = list(set(next_transitions))
        
        # Filter out transitions that are not part of the loop body
        loop_body_transitions = (
            self.loop_state_manager.entry_transitions +
            self.loop_state_manager.exit_transitions
        )
        
        next_transitions = [
            t for t in next_transitions if t in loop_body_transitions
        ]
        
        return next_transitions
    
    async def _format_iteration_result(
        self,
        iteration_results: Dict[str, Any],
        output_routing: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Format the final result for this iteration.
        
        This method formats the results according to the loop's
        output routing configuration and dual output system.
        """
        # Extract the main result (usually from exit transitions)
        exit_transitions = self.loop_state_manager.exit_transitions
        main_result = None
        
        for exit_transition in exit_transitions:
            if exit_transition in iteration_results:
                main_result = iteration_results[exit_transition]
                break
        
        if main_result is None:
            main_result = iteration_results
        
        # Format for dual output system
        return {
            "iteration_index": self.current_iteration_index,
            "iteration_item": self.current_iteration_item,
            "result": main_result,
            "all_results": iteration_results,
            "status": "completed",
            "output_type": "iteration_result"
        }
    
    async def _handle_iteration_error(
        self,
        error: Exception,
        loop_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Handle errors that occur during iteration execution.
        
        Args:
            error: The exception that occurred
            loop_config: Loop configuration for error handling
            
        Returns:
            Error result dictionary
        """
        error_handling = loop_config.get("error_handling", {})
        on_error = error_handling.get("on_iteration_error", "continue")
        
        self.logger.error(f"❌ Iteration error: {str(error)}")
        
        return {
            "iteration_index": self.current_iteration_index,
            "iteration_item": self.current_iteration_item,
            "error": str(error),
            "error_type": type(error).__name__,
            "status": "failed",
            "output_type": "iteration_error"
        }
