from dataclasses import dataclass


@dataclass
class ConnectionConfig:
    """Configuration for connection parameters."""

    max_retries: int = 3
    retry_delay: float = 1.0
    connection_pool_size: int = 10
    enable_health_check: bool = True
    health_check_interval: float = 60.0  # 1 minute


class MCPClientError(Exception):
    """Base exception for MCP client errors."""

    pass


class AuthenticationError(MCPClientError):
    """Authentication-related errors."""

    pass


class ConnectionError(MCPClientError):
    """Connection-related errors."""

    pass


class ProtocolError(MCPClientError):
    """Protocol-related errors."""

    pass
